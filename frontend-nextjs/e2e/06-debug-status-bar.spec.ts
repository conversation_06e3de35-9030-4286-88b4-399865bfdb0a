import { test, expect } from '@playwright/test';

test.describe('Debug Status Bar', () => {
  test('should debug status bar content', async ({ page }) => {
    await page.goto('/air-duct-sizer');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Find status bar
    const statusBar = page.locator('.bg-white.border-t');
    await expect(statusBar).toBeVisible();
    
    // Get all text content from status bar
    const statusBarText = await statusBar.textContent();
    console.log('Status bar text:', statusBarText);
    
    // Get all text elements in status bar
    const textElements = statusBar.locator('span');
    const count = await textElements.count();
    console.log('Number of span elements:', count);
    
    for (let i = 0; i < count; i++) {
      const text = await textElements.nth(i).textContent();
      console.log(`Span ${i}:`, text);
    }
    
    // Check if currentProject exists
    const projectExists = await page.evaluate(() => {
      return window.useProjectStore?.getState?.()?.currentProject !== null;
    });
    console.log('Current project exists:', projectExists);
    
    // Check user tier
    const userTier = await page.evaluate(() => {
      return window.useAuthStore?.getState?.()?.user?.tier || 'unknown';
    });
    console.log('User tier:', userTier);
  });
});
