# Page snapshot

```yaml
- banner:
  - heading "Air Duct Sizer" [level=1]
  - text: New Air Duct Project Free 0 rooms, 0 segments
- toolbar "Drawing tools":
  - group "Drawing tools":
    - text: Tools
    - button "Select tool - Select and move objects" [pressed]:
      - img
      - text: Select V
    - button "Room tool - Draw rooms and spaces":
      - img
      - text: Room R
    - button "Duct tool - Draw duct segments":
      - img
      - text: Duct D
    - button "Equipment tool - Place HVAC equipment":
      - img
      - text: Equipment E
    - button "Pan tool - Pan and navigate the canvas":
      - img
      - text: Pan H
  - text: View
  - button "Zoom In":
    - img
    - text: Zoom In
  - button "Zoom Out":
    - img
    - text: Zoom Out
  - button "Reset":
    - img
    - text: Reset
  - text: "Zoom: 100% Grid"
  - button "Show Grid":
    - img
    - text: Show Grid
  - button "Snap to Grid":
    - img
    - text: Snap to Grid
  - text: "Grid: 20px"
- text: "Tool: SELECT Grid: ON | Snap: ON Zoom: 100% Rooms: 0 | Segments: 0 | Equipment: 0 Shortcuts: V: Select | R: Room | D: Duct | E: Equipment | H: Pan G: Toggle Grid | S: Toggle Snap | ESC: Clear Selection"
- img
- heading "Project Properties" [level=2]
- button:
  - img
- button "Project properties panel":
  - img
  - text: Project
- button "Room properties panel" [disabled]:
  - img
  - text: Room
- button "Segment properties panel" [disabled]:
  - img
  - text: Segment
- button "Equipment properties panel" [disabled]:
  - img
  - text: Equipment
- heading "Project Information" [level=3]
- button "Edit"
- text: Project Name *
- paragraph: New Air Duct Project
- text: Location *
- paragraph: Enter location
- text: User Name
- paragraph: Demo User
- text: Contractor
- paragraph: Not specified
- heading "Codes & Standards" [level=3]
- checkbox "SMACNA" [disabled]
- text: SMACNA
- checkbox "ASHRAE" [disabled]
- text: ASHRAE
- checkbox "UL" [disabled]
- text: UL
- checkbox "IMC" [disabled]
- text: IMC
- checkbox "NFPA" [disabled]
- text: NFPA
- heading "Computational Properties" [level=3]
- img
- text: Pro Only Default Velocity (FPM)
- paragraph: "1200"
- img
- text: Pressure Class
- paragraph: Medium
- img
- text: Altitude (ft)
- paragraph: "0"
- img
- text: Friction Rate (in. w.g./100ft)
- paragraph: "0.08"
- img
- heading "Actions" [level=3]
- button "Save Project":
  - img
  - text: Save Project
- button "Export Project":
  - img
  - text: Export Project
- button "Import Project":
  - img
  - text: Import Project
- text: "Last modified: 7/14/2025, 5:24:33 PM Objects: 0 Ready • 0 rooms, 0 segments Grid: 20px Zoom: 100%"
- alert
- button "Open Next.js Dev Tools":
  - img
```