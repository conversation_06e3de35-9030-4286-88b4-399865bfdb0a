@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom keyframes for background animation */
@keyframes moveBackground {
  from {
    background-position: 0% 0%;
  }
  to {
    background-position: 0% -1000%;
  }
}

/* Ensure backdrop-filter works properly */
@supports (backdrop-filter: blur(10px)) {
  .backdrop-blur {
    backdrop-filter: blur(10px);
  }
}

/* Fallback for browsers that don't support backdrop-filter */
@supports not (backdrop-filter: blur(10px)) {
  .backdrop-blur {
    background: rgba(255, 255, 255, 0.1);
  }
}
