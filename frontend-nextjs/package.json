{"name": "sizewise-suite-nextjs-frontend", "version": "0.1.0", "private": true, "description": "Next.js frontend with glassmorphism UI for SizeWise Suite HVAC platform", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "cypress:open": "cypress open", "cypress:run": "cypress run"}, "dependencies": {"@react-pdf/renderer": "^3.1.14", "clsx": "^2.0.0", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "konva": "^9.2.0", "lucide-react": "^0.294.0", "next": "15.3.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-konva": "^18.2.10", "use-debounce": "^10.0.0", "zustand": "^4.4.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/node": "^20.17.10", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "autoprefixer": "^10.4.20", "cypress": "^13.6.1", "eslint": "^8.57.1", "eslint-config-next": "15.3.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.5.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5.7.2"}, "keywords": ["nextjs", "glassmorphism", "hvac", "typescript", "tailwindcss"], "author": "SizeWise Suite Team", "license": "MIT"}