# 1.3 Scope and Audience

# Scope and Audience

This section defines the scope and primary audience of the SizeWise Suite platform. It outlines the purpose and boundaries of this document, emphasizing its role in aligning stakeholders around the functional, architectural, and compliance expectations of the full Suite. While individual tools—such as the Air Duct Sizer, Boiler Vent Sizer,Grease Duct Sizer, Engine Exhaust Sizer, and Estimating Tool—will each have their own dedicated specifications, this section addresses the shared framework they operate within.

# Intended Audience

This document is intended for the following roles, each of whom plays a critical part in the successful development and maintenance of the SizeWise Suite:

Engineers – Responsible for implementing shared calculation logic, ensuring standards alignment, and integrating features across multiple HVAC sizing and estimating modules.

QA Analysts and Test Engineers – Charged with validating tool behavior, enforcing platform-wide compliance rules, and ensuring stable performance across both individual modules and shared platform features.

Product Managers – Oversee strategic planning, inter-tool coordination, roadmap development, and delivery priorities across the SizeWise Suite.

Technical Writers and Documentation Specialists – Develop user-facing and internal documentation at both the platform and tool levels, ensuring consistency in terminology, interface behavior, and documentation structure.

# Document Scope

This document provides guidance at the platform level across the following three areas:

Functional Scope: Defines how SizeWise Suite functions as a unified system. This includes shared interface patterns, standard input/output behavior, centralized validation logic, and the high-level interaction models that govern how tools connect. Detailed behaviors specific to each tool are covered in their respective documents.

Architectural Scope: Describes the modular design of the platform, which supports independent tool development while maintaining a coherent system architecture. It includes standardized UI components (e.g., buttons, dropdowns), centralized schema validation using AJV/Zod, plugin-ready extensibility, and offline-first architecture. It also sets structural requirements for tool-level directories and shared services.

Compliance Scope: Explains how SizeWise Suite ensures adherence to HVAC industry codes and standards (e.g., SMACNA, NFPA 54, UL 1738). It outlines common validation enforcement mechanisms, unit conversion protocols, and the process for applying regulatory updates across all tools.

By defining the platform-wide context and clarifying the role of each audience, this section ensures that engineers can build within an aligned technical foundation, QA teams can execute repeatable validation strategies, product managers can drive coordinated development, and documentation remains consistent throughout the Suite. Tool specific logic, validations, and UI flows will be detailed in their respective dedicated documents.

<!-- 1.3 Scope and Audience -->

