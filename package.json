{"name": "sizewise-suite", "version": "0.1.0", "description": "A modular, offline-capable HVAC engineering and estimating platform with glassmorphism UI", "main": "frontend/index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest tests/integration --testTimeout=30000", "test:unit": "jest tests/unit", "test:all": "./scripts/test.sh", "test:backend": "python -m pytest tests/unit/backend/ -v", "lint": "eslint frontend/ --ext .js,.ts", "lint:fix": "eslint frontend/ --ext .js,.ts --fix", "format": "prettier --write frontend/", "start:backend": "python run_backend.py", "start:dev": "concurrently \"npm run start:backend\" \"npm run dev\"", "build:pwa": "vite build && workbox generateSW workbox-config.js", "dev:nextjs": "cd frontend-nextjs && npm run dev", "build:nextjs": "cd frontend-nextjs && npm run build", "start:nextjs": "cd frontend-nextjs && npm start", "install:nextjs": "cd frontend-nextjs && npm install"}, "keywords": ["hvac", "engineering", "duct-sizing", "estimating", "offline-first", "pwa", "glassmorphism", "nextjs", "typescript"], "author": "SizeWise Suite Team", "license": "MIT", "devDependencies": {"@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "@types/jest": "^29.5.5", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "babel-jest": "^29.7.0", "concurrently": "^8.2.1", "eslint": "^8.50.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "fake-indexeddb": "^4.0.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.0.3", "typescript": "^5.2.2", "vite": "^7.0.4", "vite-plugin-pwa": "^1.0.1", "workbox-cli": "^7.0.0"}, "dependencies": {"ajv": "^8.12.0", "dexie": "^3.2.4", "idb": "^7.1.1", "zod": "^3.22.4"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}