<html><head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"/><title>Boiler Vent Sizer</title><style>
/* cspell:disable-file */
/* webkit printing magic: print all background colors */
html {
	-webkit-print-color-adjust: exact;
}
* {
	box-sizing: border-box;
	-webkit-print-color-adjust: exact;
}

html,
body {
	margin: 0;
	padding: 0;
}
@media only screen {
	body {
		margin: 2em auto;
		max-width: 900px;
		color: rgb(55, 53, 47);
	}
}

body {
	line-height: 1.5;
	white-space: pre-wrap;
}

a,
a.visited {
	color: inherit;
	text-decoration: underline;
}

.pdf-relative-link-path {
	font-size: 80%;
	color: #444;
}

h1,
h2,
h3 {
	letter-spacing: -0.01em;
	line-height: 1.2;
	font-weight: 600;
	margin-bottom: 0;
}

.page-title {
	font-size: 2.5rem;
	font-weight: 700;
	margin-top: 0;
	margin-bottom: 0.75em;
}

h1 {
	font-size: 1.875rem;
	margin-top: 1.875rem;
}

h2 {
	font-size: 1.5rem;
	margin-top: 1.5rem;
}

h3 {
	font-size: 1.25rem;
	margin-top: 1.25rem;
}

.source {
	border: 1px solid #ddd;
	border-radius: 3px;
	padding: 1.5em;
	word-break: break-all;
}

.callout {
	border-radius: 3px;
	padding: 1rem;
}

figure {
	margin: 1.25em 0;
	page-break-inside: avoid;
}

figcaption {
	opacity: 0.5;
	font-size: 85%;
	margin-top: 0.5em;
}

mark {
	background-color: transparent;
}

.indented {
	padding-left: 1.5em;
}

hr {
	background: transparent;
	display: block;
	width: 100%;
	height: 1px;
	visibility: visible;
	border: none;
	border-bottom: 1px solid rgba(55, 53, 47, 0.09);
}

img {
	max-width: 100%;
}

@media only print {
	img {
		max-height: 100vh;
		object-fit: contain;
	}
}

@page {
	margin: 1in;
}

.collection-content {
	font-size: 0.875rem;
}

.column-list {
	display: flex;
	justify-content: space-between;
}

.column {
	padding: 0 1em;
}

.column:first-child {
	padding-left: 0;
}

.column:last-child {
	padding-right: 0;
}

.table_of_contents-item {
	display: block;
	font-size: 0.875rem;
	line-height: 1.3;
	padding: 0.125rem;
}

.table_of_contents-indent-1 {
	margin-left: 1.5rem;
}

.table_of_contents-indent-2 {
	margin-left: 3rem;
}

.table_of_contents-indent-3 {
	margin-left: 4.5rem;
}

.table_of_contents-link {
	text-decoration: none;
	opacity: 0.7;
	border-bottom: 1px solid rgba(55, 53, 47, 0.18);
}

table,
th,
td {
	border: 1px solid rgba(55, 53, 47, 0.09);
	border-collapse: collapse;
}

table {
	border-left: none;
	border-right: none;
}

th,
td {
	font-weight: normal;
	padding: 0.25em 0.5em;
	line-height: 1.5;
	min-height: 1.5em;
	text-align: left;
}

th {
	color: rgba(55, 53, 47, 0.6);
}

ol,
ul {
	margin: 0;
	margin-block-start: 0.6em;
	margin-block-end: 0.6em;
}

li > ol:first-child,
li > ul:first-child {
	margin-block-start: 0.6em;
}

ul > li {
	list-style: disc;
}

ul.to-do-list {
	padding-inline-start: 0;
}

ul.to-do-list > li {
	list-style: none;
}

.to-do-children-checked {
	text-decoration: line-through;
	opacity: 0.375;
}

ul.toggle > li {
	list-style: none;
}

ul {
	padding-inline-start: 1.7em;
}

ul > li {
	padding-left: 0.1em;
}

ol {
	padding-inline-start: 1.6em;
}

ol > li {
	padding-left: 0.2em;
}

.mono ol {
	padding-inline-start: 2em;
}

.mono ol > li {
	text-indent: -0.4em;
}

.toggle {
	padding-inline-start: 0em;
	list-style-type: none;
}

/* Indent toggle children */
.toggle > li > details {
	padding-left: 1.7em;
}

.toggle > li > details > summary {
	margin-left: -1.1em;
}

.selected-value {
	display: inline-block;
	padding: 0 0.5em;
	background: rgba(206, 205, 202, 0.5);
	border-radius: 3px;
	margin-right: 0.5em;
	margin-top: 0.3em;
	margin-bottom: 0.3em;
	white-space: nowrap;
}

.collection-title {
	display: inline-block;
	margin-right: 1em;
}

.page-description {
	margin-bottom: 2em;
}

.simple-table {
	margin-top: 1em;
	font-size: 0.875rem;
	empty-cells: show;
}
.simple-table td {
	height: 29px;
	min-width: 120px;
}

.simple-table th {
	height: 29px;
	min-width: 120px;
}

.simple-table-header-color {
	background: rgb(247, 246, 243);
	color: black;
}
.simple-table-header {
	font-weight: 500;
}

time {
	opacity: 0.5;
}

.icon {
	display: inline-block;
	max-width: 1.2em;
	max-height: 1.2em;
	text-decoration: none;
	vertical-align: text-bottom;
	margin-right: 0.5em;
}

img.icon {
	border-radius: 3px;
}

.user-icon {
	width: 1.5em;
	height: 1.5em;
	border-radius: 100%;
	margin-right: 0.5rem;
}

.user-icon-inner {
	font-size: 0.8em;
}

.text-icon {
	border: 1px solid #000;
	text-align: center;
}

.page-cover-image {
	display: block;
	object-fit: cover;
	width: 100%;
	max-height: 30vh;
}

.page-header-icon {
	font-size: 3rem;
	margin-bottom: 1rem;
}

.page-header-icon-with-cover {
	margin-top: -0.72em;
	margin-left: 0.07em;
}

.page-header-icon img {
	border-radius: 3px;
}

.link-to-page {
	margin: 1em 0;
	padding: 0;
	border: none;
	font-weight: 500;
}

p > .user {
	opacity: 0.5;
}

td > .user,
td > time {
	white-space: nowrap;
}

input[type="checkbox"] {
	transform: scale(1.5);
	margin-right: 0.6em;
	vertical-align: middle;
}

p {
	margin-top: 0.5em;
	margin-bottom: 0.5em;
}

.image {
	border: none;
	margin: 1.5em 0;
	padding: 0;
	border-radius: 0;
	text-align: center;
}

.code,
code {
	background: rgba(135, 131, 120, 0.15);
	border-radius: 3px;
	padding: 0.2em 0.4em;
	border-radius: 3px;
	font-size: 85%;
	tab-size: 2;
}

code {
	color: #eb5757;
}

.code {
	padding: 1.5em 1em;
}

.code-wrap {
	white-space: pre-wrap;
	word-break: break-all;
}

.code > code {
	background: none;
	padding: 0;
	font-size: 100%;
	color: inherit;
}

blockquote {
	font-size: 1.25em;
	margin: 1em 0;
	padding-left: 1em;
	border-left: 3px solid rgb(55, 53, 47);
}

.bookmark {
	text-decoration: none;
	max-height: 8em;
	padding: 0;
	display: flex;
	width: 100%;
	align-items: stretch;
}

.bookmark-title {
	font-size: 0.85em;
	overflow: hidden;
	text-overflow: ellipsis;
	height: 1.75em;
	white-space: nowrap;
}

.bookmark-text {
	display: flex;
	flex-direction: column;
}

.bookmark-info {
	flex: 4 1 180px;
	padding: 12px 14px 14px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.bookmark-image {
	width: 33%;
	flex: 1 1 180px;
	display: block;
	position: relative;
	object-fit: cover;
	border-radius: 1px;
}

.bookmark-description {
	color: rgba(55, 53, 47, 0.6);
	font-size: 0.75em;
	overflow: hidden;
	max-height: 4.5em;
	word-break: break-word;
}

.bookmark-href {
	font-size: 0.75em;
	margin-top: 0.25em;
}

.sans { font-family: ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol"; }
.code { font-family: "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace; }
.serif { font-family: Lyon-Text, Georgia, ui-serif, serif; }
.mono { font-family: iawriter-mono, Nitti, Menlo, Courier, monospace; }
.pdf .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK JP'; }
.pdf:lang(zh-CN) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK SC'; }
.pdf:lang(zh-TW) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK TC'; }
.pdf:lang(ko-KR) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK KR'; }
.pdf .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK JP'; }
.pdf:lang(zh-CN) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC'; }
.pdf:lang(zh-TW) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK TC'; }
.pdf:lang(ko-KR) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK KR'; }
.pdf .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK JP'; }
.pdf:lang(zh-CN) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK SC'; }
.pdf:lang(zh-TW) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK TC'; }
.pdf:lang(ko-KR) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK KR'; }
.pdf .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK JP'; }
.pdf:lang(zh-CN) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC'; }
.pdf:lang(zh-TW) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK TC'; }
.pdf:lang(ko-KR) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK KR'; }
.highlight-default {
	color: rgba(50, 48, 44, 1);
}
.highlight-gray {
	color: rgba(115, 114, 110, 1);
	fill: rgba(115, 114, 110, 1);
}
.highlight-brown {
	color: rgba(159, 107, 83, 1);
	fill: rgba(159, 107, 83, 1);
}
.highlight-orange {
	color: rgba(217, 115, 13, 1);
	fill: rgba(217, 115, 13, 1);
}
.highlight-yellow {
	color: rgba(203, 145, 47, 1);
	fill: rgba(203, 145, 47, 1);
}
.highlight-teal {
	color: rgba(68, 131, 97, 1);
	fill: rgba(68, 131, 97, 1);
}
.highlight-blue {
	color: rgba(51, 126, 169, 1);
	fill: rgba(51, 126, 169, 1);
}
.highlight-purple {
	color: rgba(144, 101, 176, 1);
	fill: rgba(144, 101, 176, 1);
}
.highlight-pink {
	color: rgba(193, 76, 138, 1);
	fill: rgba(193, 76, 138, 1);
}
.highlight-red {
	color: rgba(205, 60, 58, 1);
	fill: rgba(205, 60, 58, 1);
}
.highlight-default_background {
	color: rgba(50, 48, 44, 1);
}
.highlight-gray_background {
	background: rgba(248, 248, 247, 1);
}
.highlight-brown_background {
	background: rgba(244, 238, 238, 1);
}
.highlight-orange_background {
	background: rgba(251, 236, 221, 1);
}
.highlight-yellow_background {
	background: rgba(251, 243, 219, 1);
}
.highlight-teal_background {
	background: rgba(237, 243, 236, 1);
}
.highlight-blue_background {
	background: rgba(231, 243, 248, 1);
}
.highlight-purple_background {
	background: rgba(248, 243, 252, 1);
}
.highlight-pink_background {
	background: rgba(252, 241, 246, 1);
}
.highlight-red_background {
	background: rgba(253, 235, 236, 1);
}
.block-color-default {
	color: inherit;
	fill: inherit;
}
.block-color-gray {
	color: rgba(115, 114, 110, 1);
	fill: rgba(115, 114, 110, 1);
}
.block-color-brown {
	color: rgba(159, 107, 83, 1);
	fill: rgba(159, 107, 83, 1);
}
.block-color-orange {
	color: rgba(217, 115, 13, 1);
	fill: rgba(217, 115, 13, 1);
}
.block-color-yellow {
	color: rgba(203, 145, 47, 1);
	fill: rgba(203, 145, 47, 1);
}
.block-color-teal {
	color: rgba(68, 131, 97, 1);
	fill: rgba(68, 131, 97, 1);
}
.block-color-blue {
	color: rgba(51, 126, 169, 1);
	fill: rgba(51, 126, 169, 1);
}
.block-color-purple {
	color: rgba(144, 101, 176, 1);
	fill: rgba(144, 101, 176, 1);
}
.block-color-pink {
	color: rgba(193, 76, 138, 1);
	fill: rgba(193, 76, 138, 1);
}
.block-color-red {
	color: rgba(205, 60, 58, 1);
	fill: rgba(205, 60, 58, 1);
}
.block-color-default_background {
	color: inherit;
	fill: inherit;
}
.block-color-gray_background {
	background: rgba(248, 248, 247, 1);
}
.block-color-brown_background {
	background: rgba(244, 238, 238, 1);
}
.block-color-orange_background {
	background: rgba(251, 236, 221, 1);
}
.block-color-yellow_background {
	background: rgba(251, 243, 219, 1);
}
.block-color-teal_background {
	background: rgba(237, 243, 236, 1);
}
.block-color-blue_background {
	background: rgba(231, 243, 248, 1);
}
.block-color-purple_background {
	background: rgba(248, 243, 252, 1);
}
.block-color-pink_background {
	background: rgba(252, 241, 246, 1);
}
.block-color-red_background {
	background: rgba(253, 235, 236, 1);
}
.select-value-color-default { background-color: rgba(84, 72, 49, 0.08); }
.select-value-color-gray { background-color: rgba(84, 72, 49, 0.15); }
.select-value-color-brown { background-color: rgba(210, 162, 141, 0.35); }
.select-value-color-orange { background-color: rgba(224, 124, 57, 0.27); }
.select-value-color-yellow { background-color: rgba(236, 191, 66, 0.39); }
.select-value-color-green { background-color: rgba(123, 183, 129, 0.27); }
.select-value-color-blue { background-color: rgba(93, 165, 206, 0.27); }
.select-value-color-purple { background-color: rgba(168, 129, 197, 0.27); }
.select-value-color-pink { background-color: rgba(225, 136, 179, 0.27); }
.select-value-color-red { background-color: rgba(244, 171, 159, 0.4); }

.checkbox {
	display: inline-flex;
	vertical-align: text-bottom;
	width: 16;
	height: 16;
	background-size: 16px;
	margin-left: 2px;
	margin-right: 5px;
}

.checkbox-on {
	background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20width%3D%2216%22%20height%3D%2216%22%20fill%3D%22%2358A9D7%22%2F%3E%0A%3Cpath%20d%3D%22M6.71429%2012.2852L14%204.9995L12.7143%203.71436L6.71429%209.71378L3.28571%206.2831L2%207.57092L6.71429%2012.2852Z%22%20fill%3D%22white%22%2F%3E%0A%3C%2Fsvg%3E");
}

.checkbox-off {
	background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20x%3D%220.75%22%20y%3D%220.75%22%20width%3D%2214.5%22%20height%3D%2214.5%22%20fill%3D%22white%22%20stroke%3D%22%2336352F%22%20stroke-width%3D%221.5%22%2F%3E%0A%3C%2Fsvg%3E");
}
	
</style></head><body><article id="21d2881f-86f1-8098-9799-f9e30919ec6f" class="page sans"><header><div class="page-header-icon undefined"><span class="icon">📄</span></div><h1 class="page-title">Boiler Vent Sizer</h1><p class="page-description"></p></header><div class="page-body"><h3 id="21d2881f-86f1-808e-bf4b-c618d4d1eadc" class=""><strong>Blueprint for bullet-proof, leave-nothing-out documentation</strong></h3><p id="21d2881f-86f1-8040-ad99-f47acde7c607" class="">Below is a documentation architecture I’d propose for the boiler-flue library we just outlined.  The goal is <strong>zero silent assumptions</strong>: every formula, unit, constant, design caveat, and code contract is spelled out once and is easy to verify, update, or audit.</p><hr id="21d2881f-86f1-80b0-9218-f943da40a058"/><h2 id="21d2881f-86f1-80ad-8dfc-d868802224bd" class=""><strong>1. Overall folder layout</strong></h2><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="21d2881f-86f1-8001-8162-dfd0dff373de" class="code"><code class="language-Plain Text" style="white-space:pre-wrap;word-break:break-all">docs/
│
├─ index.md               ← high-level overview, cross-links every section
├─ quickstart.md          ← 10-minute “hello-world” walkthrough
├─ concepts/              ← engineering background &amp; derivations
│   ├─ flue_velocity.md
│   ├─ vent_sizing.md
│   ├─ pressure_draft.md
│   ├─ combustion_air.md
│   └─ condensation_temp.md
│
├─ api/                   ← auto-generated docs from code (Sphinx + autodoc)
│   ├─ flue_velocity.rst
│   ├─ vent_sizing.rst
│   ├─ …
│
├─ reference/             ← tables, constants, unit conversions, code excerpts
│   ├─ constants.md
│   └─ units.md
│
├─ examples/              ← real-world, copy-paste-ready notebooks / scripts
│   ├─ sizing_single_boiler.ipynb
│   ├─ multi_boiler_stack.ipynb
│   └─ combustion_air_calc.py
│
├─ standards/             ← pointers to NFPA 54, ASHRAE, EN 13384, etc.
│   └─ summary.md
│
├─ change_log.md          ← every change, why, who, link to PR / ticket
└─ how_to_contribute.md   ← style guide, CI, lint, docstring rules</code></pre><h3 id="21d2881f-86f1-8045-bd2c-d6bd5cf740d7" class=""><strong>Why this structure?</strong></h3><table id="21d2881f-86f1-8091-b14b-cb5f4fcc1b4b" class="simple-table"><tbody><tr id="21d2881f-86f1-8026-8cec-e523d2456c0e"><td id="Q?vv" class=""><strong>Directory</strong></td><td id="ZTlP" class=""><strong>Purpose</strong></td></tr><tr id="21d2881f-86f1-8092-8295-cfe0f08b523b"><td id="Q?vv" class="">concepts/</td><td id="ZTlP" class=""><strong>Narrative</strong> explanations &amp; derivations; anyone can read without opening the code.</td></tr><tr id="21d2881f-86f1-8078-84ae-fe6f3b84f97e"><td id="Q?vv" class="">api/</td><td id="ZTlP" class=""><strong>Exact function signatures</strong> rendered from docstrings—no drift between code &amp; docs.</td></tr><tr id="21d2881f-86f1-80d0-bff3-d060f26082a6"><td id="Q?vv" class="">examples/</td><td id="ZTlP" class=""><strong>Executable</strong> demonstrations; also serve as tests (fail if doc becomes incorrect).</td></tr><tr id="21d2881f-86f1-801c-902d-f3d8fd3abdd9"><td id="Q?vv" class="">reference/</td><td id="ZTlP" class="">One canonical place for constants, unit tables, symbol glossary.</td></tr><tr id="21d2881f-86f1-8050-9fd5-d27f3afd3687"><td id="Q?vv" class="">standards/</td><td id="ZTlP" class="">Compliance breadcrumbs &amp; exact clause numbers—useful during audits or peer review.</td></tr></tbody></table><hr id="21d2881f-86f1-80c1-8b85-fac6fd808f5c"/><h2 id="21d2881f-86f1-80fe-aa6b-d3079316b763" class=""><strong>2. Content checklist (per formula family)</strong></h2><p id="21d2881f-86f1-803a-acc0-f17b27db8ffc" class="">For every topic file under concepts/:</p><table id="21d2881f-86f1-8088-82a9-ec9521bd8f74" class="simple-table"><tbody><tr id="21d2881f-86f1-80fd-8513-db28c76f65bc"><td id="^qCo" class=""><strong>Section</strong></td><td id="sjUx" class=""><strong>Must Include</strong></td><td id="bo{U" class=""><strong>Rationale</strong></td></tr><tr id="21d2881f-86f1-8043-b4c4-e012e15c9e4e"><td id="^qCo" class=""><strong>Problem Scope</strong></td><td id="sjUx" class="">What physical question the formulas answer.</td><td id="bo{U" class="">Anchors reader’s expectations.</td></tr><tr id="21d2881f-86f1-80c5-a640-f5eb2471cc0b"><td id="^qCo" class=""><strong>Derivation / Basis</strong></td><td id="sjUx" class="">Step-by-step derivation or reference to standard derivation (with page #/clause).</td><td id="bo{U" class="">Prevents black-box syndrome; helps future reviewers check validity.</td></tr><tr id="21d2881f-86f1-80e6-90e7-f2c6f44dc477"><td id="^qCo" class=""><strong>Final Equations</strong></td><td id="sjUx" class="">Boxed formulas with variable table (symbol, meaning, units, default).</td><td id="bo{U" class="">Eliminates ambiguity—every symbol documented once.</td></tr><tr id="21d2881f-86f1-80de-8975-cb951e29a831"><td id="^qCo" class=""><strong>Design Ranges</strong></td><td id="sjUx" class="">Typical values (e.g. draft pressures, velocities) and flags for out-of-range use.</td><td id="bo{U" class="">Guides engineers during sizing; flags when extrapolation is risky.</td></tr><tr id="21d2881f-86f1-80f5-b722-c7a5d0924a36"><td id="^qCo" class=""><strong>Assumptions &amp; Limitations</strong></td><td id="sjUx" class="">List of simplifications (ideal gas, steady flow, etc.).</td><td id="bo{U" class="">Makes hidden premises explicit.</td></tr><tr id="21d2881f-86f1-8021-ba60-f688125ffbbf"><td id="^qCo" class=""><strong>Worked Example</strong></td><td id="sjUx" class="">Hand-calculated example + code snippet.</td><td id="bo{U" class="">Confirms the math and demonstrates API usage.</td></tr><tr id="21d2881f-86f1-806a-bc70-e03c46cbc0f3"><td id="^qCo" class=""><strong>Validation Checks</strong></td><td id="sjUx" class="">Unit tests / assertions the example must pass (e.g. within 1 % of handbook value).</td><td id="bo{U" class="">Prevents silent regression.</td></tr><tr id="21d2881f-86f1-804d-8262-c693ef033d71"><td id="^qCo" class=""><strong>References</strong></td><td id="sjUx" class="">Standards, textbooks, manufacturer data sheets (properly cited).</td><td id="bo{U" class="">Traceability.</td></tr></tbody></table><p id="21d2881f-86f1-80c0-86f0-fb0a05048f2c" class=""><em>(A template markdown snippet for these headings lives in how_to_contribute.md so all new pages are uniform.)</em></p><hr id="21d2881f-86f1-80f6-82c6-ee298c8018b8"/><h2 id="21d2881f-86f1-8060-a9e1-ceee8c8a6332" class=""><strong>3. Tooling &amp; automation to keep docs 100 % in-sync</strong></h2><table id="21d2881f-86f1-808c-ae47-d5d78fbe25db" class="simple-table"><tbody><tr id="21d2881f-86f1-8010-815e-fe637fad9146"><td id="??&gt;]" class=""><strong>Tool</strong></td><td id="NVPH" class=""><strong>What it enforces</strong></td><td id="PYD|" class=""><strong>How it avoids missing details</strong></td></tr><tr id="21d2881f-86f1-8061-b85e-d1d100ef4d5a"><td id="??&gt;]" class=""><strong>Sphinx + autodoc (or MkDocs-Material)</strong></td><td id="NVPH" class="">Pulls docstrings directly → api/ HTML/markdown.</td><td id="PYD|" class="">If a parameter, return type, or unit is undocumented in code, CI fails.</td></tr><tr id="21d2881f-86f1-8047-b568-df4a8308134c"><td id="??&gt;]" class=""><strong>doctest / nbval</strong></td><td id="NVPH" class="">Executes every code block in concepts/ &amp; every notebook in examples/.</td><td id="PYD|" class="">Guarantees that examples stay runnable after code changes.</td></tr><tr id="21d2881f-86f1-80cc-9abf-f2c59e52765a"><td id="??&gt;]" class=""><strong>pydantic + type-hints lint</strong></td><td id="NVPH" class="">All functions must declare units in the docstring and types in code.</td><td id="PYD|" class="">Prevents “magic float” arguments; forces explicit unit annotations.</td></tr><tr id="21d2881f-86f1-809c-9425-ddca6b04e815"><td id="??&gt;]" class=""><strong>CI badge</strong></td><td id="NVPH" class="">Docs build &amp; doctest status visible on README.</td><td id="PYD|" class="">Team sees instantly if docs drift.</td></tr><tr id="21d2881f-86f1-80c6-8061-fc44f4e44e0c"><td id="??&gt;]" class=""><strong>Change‐log gate</strong></td><td id="NVPH" class="">Merge rule: PR must update change_log.md &amp; at least one doc page if behavior changes.</td><td id="PYD|" class="">Institutional memory; no silent scope creep.</td></tr></tbody></table><hr id="21d2881f-86f1-80a3-b8e8-fcf24ffa930b"/><h2 id="21d2881f-86f1-80a0-95a0-e220d2fcd570" class=""><strong>4. Depth vs. usability balance</strong></h2><p id="21d2881f-86f1-8038-91ec-da2d5d405bb9" class=""><em>Intellectual-sparring lens:</em></p><ul id="21d2881f-86f1-801a-90a8-fa0338d5e803" class="bulleted-list"><li style="list-style-type:disc"><strong>Assumption analysis:</strong> Each derivation page begins with an <strong>Assumptions</strong> call-out box—easy to challenge if they don’t hold.</li></ul><ul id="21d2881f-86f1-807e-93bf-f86ef6c6b6b0" class="bulleted-list"><li style="list-style-type:disc"><strong>Counterpoints &amp; alternatives:</strong> Where multiple correlations exist (e.g., Colebrook vs. Swamee-Jain for f), doc lists pros/cons and cites comparative studies—so future engineers can debate or swap models knowingly.</li></ul><ul id="21d2881f-86f1-808e-a310-fcd113a588dc" class="bulleted-list"><li style="list-style-type:disc"><strong>“Truth over agreement” tests:</strong> Example notebooks include edge-cases (low draft, high condensing) that purposely fail when a formula is mis-applied, surfacing flawed logic instead of glossing over it.</li></ul><hr id="21d2881f-86f1-80d3-93b2-eabf7ad56951"/><h2 id="21d2881f-86f1-801e-abf7-f9d4a5508888" class=""><strong>5. Expansion strategy</strong></h2><ol type="1" id="21d2881f-86f1-801c-b94e-c0b96dd55491" class="numbered-list" start="1"><li><strong>Add new physical topic</strong> → create a file in concepts/, generate stub via template script.</li></ol><ol type="1" id="21d2881f-86f1-80b1-88a0-e9dd79a3122d" class="numbered-list" start="2"><li><strong>Write code</strong> → functions with complete docstrings; run make docs to build and test.</li></ol><ol type="1" id="21d2881f-86f1-806c-9b5a-e370e2c975ba" class="numbered-list" start="3"><li><strong>Auto-gen API page</strong> → appears under api/ without manual work.</li></ol><ol type="1" id="21d2881f-86f1-806f-b007-d3223a500ddb" class="numbered-list" start="4"><li><strong>Create example notebook</strong> in examples/; CI executes it headless.</li></ol><ol type="1" id="21d2881f-86f1-80eb-8233-f08bdb6e4a3d" class="numbered-list" start="5"><li>Link from index.md—now discoverable &amp; cross-referenced.</li></ol><hr id="21d2881f-86f1-8054-8f63-f2eac64082dc"/><h3 id="21d2881f-86f1-801f-8c7c-e5f1c13e198e" class=""><strong>Bottom line</strong></h3><ul id="21d2881f-86f1-8001-a3e9-c3c81f6689ba" class="bulleted-list"><li style="list-style-type:disc"><strong>Single-source-of-truth</strong>: All formulas live exactly once—in code Docstrings <em>and</em> narrative derivations.</li></ul><ul id="21d2881f-86f1-8064-8e57-cd6ec5e2ec94" class="bulleted-list"><li style="list-style-type:disc"><strong>Executable examples</strong>: Every doc section doubles as a test, preventing bit-rot.</li></ul><ul id="21d2881f-86f1-80f9-ac0e-d2fb7dd705e7" class="bulleted-list"><li style="list-style-type:disc"><strong>Standards cross-links</strong>: Each assumption &amp; range traces to NFPA/ASHRAE clause, so nothing important is “hand-waved.”</li></ul><p id="21d2881f-86f1-8093-965b-f07939b32804" class="">Follow this plan, and you’ll have auditors, junior engineers, and future you thanking present-you for a documentation set that is <em>complete, verifiable, and hard to break</em>.</p><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="21d2881f-86f1-8041-b5b0-cc7caa0892d1" class="code"><code class="language-Mermaid">flowchart TD
    A[Compute results] --&gt; B(classify_category)
    B --&gt; C{Lookup NFPA rules \n for that category}
    C --&gt; D[Rule 1: Draft sign &amp; limit]
    C --&gt; E[Rule 2: Condensing vs. materials]
    C --&gt; F[Rule 3: Diameter vs. capacity tables]
    C --&gt; G[Rule 4: Clearance, pitch, height]
    D --&gt;|pass| H[OK]
    E --&gt;|pass| H
    F --&gt;|pass| H
    G --&gt;|pass| H
    D --&gt;|fail| Z[Raise NFPAError]
    E --&gt;|fail| Z
    F --&gt;|fail| Z
    G --&gt;|fail| Z</code></pre><p id="21d2881f-86f1-80c3-885a-eafd7bafd7dc" class="">Below is a <strong>stress-tested design add-on</strong> that lets the code <strong>self-police every result against the NFPA 54 venting categories (I - IV)</strong> and any specific numeric limits you want to enforce (draft, temperature, pressure, material, diameter tables, etc.).</p><p id="21d2881f-86f1-8036-9f3f-ce8d3c8636d3" class="">I keep the same “one-concept-per-file” principle, then layer a <strong>standards-validation module</strong> on top.</p><hr id="21d2881f-86f1-806d-a498-f1ad12bd3bae"/><h2 id="21d2881f-86f1-8036-b3f0-ecda600c47e4" class=""><strong>1 · New Source-Code Files</strong></h2><table id="21d2881f-86f1-8044-b0c7-ca4ce39ef4dd" class="simple-table"><tbody><tr id="21d2881f-86f1-8075-9cd7-c684b7f30e68"><td id="JuQo" class=""><strong>#</strong></td><td id="]DFY" class=""><strong>File</strong></td><td id=":d|^" class=""><strong>Responsibility</strong></td><td id="AJhb" class=""><strong>What lives inside</strong></td></tr><tr id="21d2881f-86f1-803d-be46-cb32ddcfd99c"><td id="JuQo" class=""><strong>8</strong></td><td id="]DFY" class="">vent_category.py</td><td id=":d|^" class=""><em>Pure logic</em> to determine the appliance/vent <strong>category</strong> from calculated data.• classify_category(static_p, dewpoint_c, flue_temp_c) returns <strong>Category I, II, III, or IV</strong>• Helper: is_condensing(flue_temp_c, dewpoint_c)</td><td id="AJhb" class="">Keeps the official ANSI Z21.13 / NFPA 54 wording in one place.</td></tr><tr id="21d2881f-86f1-80ce-81e9-dfccc792f590"><td id="JuQo" class=""><strong>9</strong></td><td id="]DFY" class="">nfpa_rules.py</td><td id=":d|^" class=""><strong>Hard data</strong> from NFPA 54 tables, split into YAML / JSON constants that the code imports.• Vent-diameter capacity tables• Combustion-air opening sizes• Clearance, pitch, height minima• Draft pass/fail limits for Cat I field test (Annex G)</td><td id="AJhb" class="">Treat it like a mini-database so you can update when the Code is revised.</td></tr><tr id="21d2881f-86f1-8012-a111-cd28d9312fe7"><td id="JuQo" class=""><strong>10</strong></td><td id="]DFY" class="">standards_validation.py</td><td id=":d|^" class="">Central <strong>validation orchestrator</strong>.Functions accept raw results and raise/return structured errors:validate_draft(res), validate_temp(res), validate_material(res), validate_capacity(res)…Each rule calls data in nfpa_rules.py <strong>and</strong> category logic in vent_category.py.</td><td id="AJhb" class="">Makes it trivial to plug the validation layer into any UI or batch pipeline.</td></tr><tr id="21d2881f-86f1-80c8-be38-e611efc395d7"><td id="JuQo" class=""><strong>11</strong></td><td id="]DFY" class="">exceptions.py</td><td id=":d|^" class="">Custom exception classes (NFPAError, OutOfCategoryError, OverCapacityError, …) so calling code can route the outcome (fail UI, warn user, or auto-resize vent).</td><td id="AJhb" class="">Keeps error handling explicit and type-safe.</td></tr></tbody></table><p id="21d2881f-86f1-80dc-bdab-f52ec2931e74" class=""><em>(Previous numbering ended at 7; the optional unit_conversion.py was #8.  You now have 11 total Python source files.)</em></p><hr id="21d2881f-86f1-805d-ad91-f3a20bac03aa"/><h2 id="21d2881f-86f1-80cd-a47c-d57fac0ecaa0" class=""><strong>2 · Validation Flowchart</strong></h2><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="21d2881f-86f1-8079-95cf-c4a8dd52f913" class="code"><code class="language-Plain Text" style="white-space:pre-wrap;word-break:break-all">flowchart TD
    A[Compute results] --&gt; B(classify_category)
    B --&gt; C{Lookup NFPA rules \n for that category}
    C --&gt; D[Rule 1: Draft sign &amp; limit]
    C --&gt; E[Rule 2: Condensing vs. materials]
    C --&gt; F[Rule 3: Diameter vs. capacity tables]
    C --&gt; G[Rule 4: Clearance, pitch, height]
    D --&gt;|pass| H[OK]
    E --&gt;|pass| H
    F --&gt;|pass| H
    G --&gt;|pass| H
    D --&gt;|fail| Z[Raise NFPAError]
    E --&gt;|fail| Z
    F --&gt;|fail| Z
    G --&gt;|fail| Z</code></pre><hr id="21d2881f-86f1-8018-8cf8-c6cb03e7053d"/><h2 id="21d2881f-86f1-8086-830f-dd3ba865e8ae" class=""><strong>3 · Category Logic (Vent_Category.py)</strong></h2><table id="21d2881f-86f1-8007-bef9-fdce4dca7814" class="simple-table"><tbody><tr id="21d2881f-86f1-80a8-94a8-c30c8ab3d83d"><td id="GVjD" class=""><strong>Parameter</strong></td><td id="X{~T" class=""><strong>NFPA 54 Definition (simplified)</strong></td><td id="Wo&gt;Y" class=""><strong>Decision Rule</strong></td></tr><tr id="21d2881f-86f1-80b3-900b-dc3b741f530f"><td id="GVjD" class=""><strong>Static pressure sign at any point in vent</strong></td><td id="X{~T" class=""><em>≤ 0</em> → “non-positive” (Cat I, II)<em>&gt; 0</em> → “positive” (Cat III, IV)</td><td id="Wo&gt;Y" class="">static_p &lt;= 0 ? nonpos : pos</td></tr><tr id="21d2881f-86f1-80b7-ab94-c965d2a79cd3"><td id="GVjD" class=""><strong>Condensing?</strong> (water forms in vent during continuous operation)</td><td id="X{~T" class=""><em>Yes</em> → “condensing” (Cat II, IV)<em>No</em>  → “non-condensing” (Cat I, III)</td><td id="Wo&gt;Y" class="">flue_temp &lt;= dewpoint + margin ? cond : noncond</td></tr><tr id="21d2881f-86f1-8018-b24a-ece29ae75c1a"><td id="GVjD" class=""><strong>Final Category</strong></td><td id="X{~T" class="">Matrix of sign × condensing</td><td id="Wo&gt;Y" class="">See table below</td></tr></tbody></table><table id="21d2881f-86f1-80b2-8ea2-e1b037e6bdc4" class="simple-table"><tbody><tr id="21d2881f-86f1-8022-ab2f-e6287577f974"><td id="g;=?" class=""></td><td id="=[vV" class=""><strong>Non-condensing</strong></td><td id="KWD=" class=""><strong>Condensing</strong></td></tr><tr id="21d2881f-86f1-8080-8e6f-f5b83e3b8555"><td id="g;=?" class=""><strong>Non-positive draft</strong></td><td id="=[vV" class=""><strong>Category I</strong></td><td id="KWD=" class=""><strong>Category II</strong></td></tr><tr id="21d2881f-86f1-80a8-96fd-d2f116dde260"><td id="g;=?" class=""><strong>Positive draft</strong></td><td id="=[vV" class=""><strong>Category III</strong></td><td id="KWD=" class=""><strong>Category IV</strong></td></tr></tbody></table><p id="21d2881f-86f1-8048-80c7-eb1bf3b54b61" class=""><em>(Exact wording pulled from NFPA 54 - 2021 §3.3.49, 3.3.53.3, and Annex A)</em></p><hr id="21d2881f-86f1-804c-8878-ccd867e0b529"/><h2 id="21d2881f-86f1-80cd-aab9-de360d169240" class=""><strong>4 · Examples of Numeric Gate-Checks (Standards_Validation.py)</strong></h2><table id="21d2881f-86f1-8002-9a14-f5d4ec6a02c6" class="simple-table"><tbody><tr id="21d2881f-86f1-807a-a2b6-f579f8c81aec"><td id="yTLD" class=""><strong>Check</strong></td><td id="K;{K" class=""><strong>NFPA 54 / IFGC Clause</strong></td><td id="bPwd" class=""><strong>Typical Limit</strong></td></tr><tr id="21d2881f-86f1-8024-b6c3-e8676c1c6150"><td id="yTLD" class=""><strong>Draft test (Cat I)</strong></td><td id="K;{K" class="">Annex G.5.2</td><td id="bPwd" class="">Draft at draft-hood relief ≥ –0.01 in w.c. (–2.5 Pa) when appliance cycling</td></tr><tr id="21d2881f-86f1-8090-a732-dcbcc3d684d0"><td id="yTLD" class=""><strong>Vent capacity vs. Table 13.1</strong></td><td id="K;{K" class="">Ch.13 sizing tables</td><td id="bPwd" class="">For a 30 ft, 4 in Type B vent, max input ~170 kBtu/h. (Function validate_capacity() checks table by diameter/height/length.)</td></tr><tr id="21d2881f-86f1-8085-adff-c91a587359af"><td id="yTLD" class=""><strong>Vent material</strong></td><td id="K;{K" class="">§5.6.7 (positive-pressure)</td><td id="bPwd" class="">“Listed special gas vent” required for Cat III/IV; fail if pipe material ≠ listed.</td></tr><tr id="21d2881f-86f1-8019-8da4-ea82a387564c"><td id="yTLD" class=""><strong>Clearance to combustibles</strong></td><td id="K;{K" class="">§5.3.14</td><td id="bPwd" class="">≥ 1 in. air space for Type B; ≥ 6 in. for single-wall.</td></tr><tr id="21d2881f-86f1-80d7-8ca6-d4fb2f78b26b"><td id="yTLD" class=""><strong>Pitch of horizontal segments</strong></td><td id="K;{K" class="">§5.3.10</td><td id="bPwd" class="">Min ¼ in. per ft (≈ 21 mm per m) upward to termination.</td></tr></tbody></table><p id="21d2881f-86f1-8011-b637-c8a661df01bd" class=""><em>(All hard numbers live in nfpa_rules.py; validation code only references them.)</em></p><hr id="21d2881f-86f1-80df-ad10-c79c587b1db5"/><h2 id="21d2881f-86f1-804d-bf9c-fe0d257e8f5e" class=""><strong>5 · Documentation Up-Grades</strong></h2><h3 id="21d2881f-86f1-80f9-8977-d7b1ba61d354" class=""><strong>New files under</strong></h3><h3 id="21d2881f-86f1-8088-8848-e51a095adf29" class=""><strong>docs/</strong></h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="21d2881f-86f1-80e2-b995-d8261f325b79" class="code"><code class="language-Plain Text" style="white-space:pre-wrap;word-break:break-all">docs/
├─ concepts/
│   └─ validation.md          ← narrative of category logic + how rules fire
├─ api/
│   ├─ vent_category.rst
│   ├─ nfpa_rules.rst
│   └─ standards_validation.rst
├─ examples/
│   └─ nfpa_check_runner.ipynb ← runs a full sizing + validation demo</code></pre><p id="21d2881f-86f1-80c1-82c6-fbab71a94cf7" class=""><strong>Content checklist for validation.md</strong></p><ol type="1" id="21d2881f-86f1-8010-a348-de255c025e5a" class="numbered-list" start="1"><li><strong>Code-and-table handshake</strong> – cite every NFPA clause used, with page/section number.</li></ol><ol type="1" id="21d2881f-86f1-80ec-ae04-ecc059ac0c5c" class="numbered-list" start="2"><li><strong>Assumption analysis</strong> – e.g., <em>“We assume ANSI Z21 category definitions are accepted locally—if your jurisdiction uses EN 15502, swap the JSON rule-set.”</em></li></ol><ol type="1" id="21d2881f-86f1-8036-b0ad-c13f34c71464" class="numbered-list" start="3"><li><strong>Counterpoints</strong> – discuss borderline cases (e.g., a Cat I boiler with induced draft fan).</li></ol><ol type="1" id="21d2881f-86f1-8039-8374-fd98752250d9" class="numbered-list" start="4"><li><strong>Alternative validations</strong> – show how to plug in a regional code (e.g., B149.1 for Canada).</li></ol><ol type="1" id="21d2881f-86f1-809e-b22b-fc9d8a265ec9" class="numbered-list" start="5"><li><strong>Unit-test matrix</strong> – table of synthetic cases that should pass/fail to guard against regressions.</li></ol><p id="21d2881f-86f1-80d9-b849-c105a65a6682" class=""><strong>CI rule:</strong> every pull-request that touches sizing logic <strong>must</strong> add/adjust at least one failing or passing test in tests/test_validation.py so coverage stays 100 %.</p><hr id="21d2881f-86f1-80e4-b92f-d9a16382cdf9"/><h2 id="21d2881f-86f1-80a9-a6ee-d4f051b2b704" class=""><strong>6 · Scrutiny &amp; Intellectual Honesty</strong></h2><table id="21d2881f-86f1-8074-8b74-f7e16e253c09" class="simple-table"><tbody><tr id="21d2881f-86f1-8078-b33c-d6a8ac571185"><td id="MCrM" class=""><strong>Assumption</strong></td><td id="^@{z" class=""><strong>Potential Flaw</strong></td><td id="Q[T&gt;" class=""><strong>Counter-measure</strong></td></tr><tr id="21d2881f-86f1-802a-a874-fa450ff840ec"><td id="MCrM" class="">NFPA 54 is always applicable.</td><td id="^@{z" class="">Philippines may use IFC/IFGC derivatives or ASEAN standards.</td><td id="Q[T&gt;" class="">Make rule-set pluggable; store code-neutral “category” API, swap JSON via env var.</td></tr><tr id="21d2881f-86f1-8052-bf93-e3aec2a21d29"><td id="MCrM" class="">Condensing vs. non-condensing decided solely by dew-point comparison.</td><td id="^@{z" class="">Real boilers can condense intermittently (startup, low fire).</td><td id="Q[T&gt;" class="">Validation raises <em>warning</em> not <em>error</em> if condensing risk is transient &lt; 10 % duty.</td></tr><tr id="21d2881f-86f1-8096-af24-ddf435337a27"><td id="MCrM" class="">Capacity tables are lookup-only.</td><td id="^@{z" class="">User may size custom stacks outside table range.</td><td id="Q[T&gt;" class="">Provide fallback Darcy-Weisbach solver and log “unlisted-vent” to audit trail.</td></tr></tbody></table><hr id="21d2881f-86f1-80ba-8b4f-e131ffeb68fa"/><h3 id="21d2881f-86f1-8062-8885-e9e6ca576956" class=""><strong>TL;DR</strong></h3><p id="21d2881f-86f1-80c1-a872-c07d875f7660" class=""><em>Add </em><em><strong>four lightweight modules</strong></em><em>—vent_category.py, nfpa_rules.py, standards_validation.py, exceptions.py—plus a data file of NFPA tables.</em></p><p id="21d2881f-86f1-80a1-b791-f5bf4d04c0a8" class=""><em>This layer classifies the appliance, checks every computed result against NFPA 54, and raises rich exceptions when anything is out of bounds, all fully documented and test-driven.</em></p><h2 id="21d2881f-86f1-80de-8a56-fb2519091620" class="">Boiler Vent Sizer - Planning Document</h2><hr id="21d2881f-86f1-8074-9c74-d37a21412410"/><h2 id="21d2881f-86f1-8052-be6e-f916a323d87b" class="">1. Overview &amp; Goals</h2><h3 id="21d2881f-86f1-80fa-ab78-cfb249125d44" class="">Purpose and Objectives</h3><ul id="21d2881f-86f1-80e5-8dd2-c04fd474f0a6" class="bulleted-list"><li style="list-style-type:disc">Implement the &quot;Boiler Vent Sizer&quot; tool as a high-priority feature within the SizeWise Suite.</li></ul><ul id="21d2881f-86f1-80ce-9652-f3e9dc7dd5d2" class="bulleted-list"><li style="list-style-type:disc">Provide HVAC engineers and estimators with a streamlined calculator for vent sizing compliant with NFPA 54 standards.</li></ul><ul id="21d2881f-86f1-80b6-8bc6-f1ddba683050" class="bulleted-list"><li style="list-style-type:disc">Facilitate rapid and accurate sizing decisions, minimizing calculation errors and ensuring compliance.</li></ul><h3 id="21d2881f-86f1-8098-aac4-d149e64b11c5" class="">Problem Statement &amp; User Benefits</h3><ul id="21d2881f-86f1-80dd-be8f-e3013fa2d411" class="bulleted-list"><li style="list-style-type:disc">Users currently lack a consolidated, standards-compliant boiler vent sizing tool.</li></ul><ul id="21d2881f-86f1-80a5-82a5-d5e7c8d1ed48" class="bulleted-list"><li style="list-style-type:disc">Expected benefits include increased productivity, reduced compliance errors, and streamlined workflow with immediate results and validations.</li></ul><hr id="21d2881f-86f1-80a5-826d-ceb459414093"/><h2 id="21d2881f-86f1-8083-8c68-f3605f9d7c2a" class="">2. Functional Requirements</h2><h3 id="21d2881f-86f1-80d1-8c14-c569d591c5a6" class="">Inputs</h3><ul id="21d2881f-86f1-8004-b084-d2224e7eb528" class="bulleted-list"><li style="list-style-type:disc">Boiler capacity (MBH/kW)</li></ul><ul id="21d2881f-86f1-805e-8678-e4bd65e2e288" class="bulleted-list"><li style="list-style-type:disc">Turn-down ratio (optional)</li></ul><ul id="21d2881f-86f1-8015-ba39-f38b440f2c37" class="bulleted-list"><li style="list-style-type:disc">Fuel type (Natural Gas, Propane, Oil)</li></ul><ul id="21d2881f-86f1-80d6-9e44-e77f566645ca" class="bulleted-list"><li style="list-style-type:disc">Flue gas temperature, ambient temperature, altitude</li></ul><ul id="21d2881f-86f1-8032-8406-cae157965e91" class="bulleted-list"><li style="list-style-type:disc">Vent material (Type B, Stainless Steel, etc.)</li></ul><ul id="21d2881f-86f1-802b-b277-e5f7193ebc0c" class="bulleted-list"><li style="list-style-type:disc">System geometry: total length, vertical height, elbows (number and equivalent length), pitch</li></ul><h3 id="21d2881f-86f1-803f-9561-f4d2c593ec54" class="">Outputs</h3><ul id="21d2881f-86f1-80d9-8ccb-c1d071abefa6" class="bulleted-list"><li style="list-style-type:disc">Required vent diameter</li></ul><ul id="21d2881f-86f1-809b-a9c9-c489738d5d2f" class="bulleted-list"><li style="list-style-type:disc">Draft pressure (+/- in. WC) at appliance and termination</li></ul><ul id="21d2881f-86f1-8048-894c-dcec1568c923" class="bulleted-list"><li style="list-style-type:disc">Flue gas velocity and temperature at exit</li></ul><ul id="21d2881f-86f1-803a-843b-fb34f62a950b" class="bulleted-list"><li style="list-style-type:disc">Combustion air requirement (CFM)</li></ul><ul id="21d2881f-86f1-806d-9fa3-ea9c34deafba" class="bulleted-list"><li style="list-style-type:disc">NFPA Category (I-IV) determination</li></ul><ul id="21d2881f-86f1-80d0-81a8-d80288dd8f21" class="bulleted-list"><li style="list-style-type:disc">Compliance validation with NFPA 54; secondary IFGC warnings</li></ul><hr id="21d2881f-86f1-8060-bc4d-ccd163fd46b2"/><h2 id="21d2881f-86f1-8011-a471-eedfa687906c" class="">3. Technical Architecture</h2><h3 id="21d2881f-86f1-803e-9326-e3940b64ad12" class="">Folder Structure</h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="21d2881f-86f1-8084-9e8b-df3ff8dc0978" class="code"><code class="language-Plain Text" style="white-space:pre-wrap;word-break:break-all">app/
├─ tools/
│   └─ boiler_venting/
│       ├─ formulas/
│       │   ├─ flue_velocity.py
│       │   ├─ vent_sizing.py
│       │   ├─ pressure_draft.py
│       │   ├─ combustion_air.py
│       │   ├─ condensation_temp.py
│       │   ├─ firing_levels.py
│       │   ├─ vent_category.py
│       │   ├─ nfpa_rules.py
│       │   ├─ standards_validation.py
│       │   └─ exceptions.py
│       ├─ schemas.py
│       ├─ api.py
│       ├─ ui.py
│       ├─ constants.py
│       └─ tests/
│           └─ (unit/integration tests)
</code></pre><h3 id="21d2881f-86f1-806e-9b7f-c710fc3c3018" class="">Technology Choices</h3><ul id="21d2881f-86f1-80b6-a6ae-db062b4e9886" class="bulleted-list"><li style="list-style-type:disc">Front-end: React 18, Vite, TypeScript, Tailwind CSS</li></ul><ul id="21d2881f-86f1-8073-b5f9-ee14da358683" class="bulleted-list"><li style="list-style-type:disc">Back-end: Flask (Python 3.11, planned future integration)</li></ul><ul id="21d2881f-86f1-8083-b7d2-ce679fa05f09" class="bulleted-list"><li style="list-style-type:disc">Deployment: AWS Lightsail (Gunicorn/Nginx)</li></ul><ul id="21d2881f-86f1-8014-ae29-c22dbd0afec5" class="bulleted-list"><li style="list-style-type:disc">Offline Mode: localStorage with JSON configuration files</li></ul><h3 id="21d2881f-86f1-8069-9116-e4ed410769e7" class="">API Endpoints</h3><ul id="21d2881f-86f1-807b-b33b-d85271b1fdfa" class="bulleted-list"><li style="list-style-type:disc"><code>/calculate</code>: Accepts input schema, returns calculation results, NFPA category, validation flags, and warnings.</li></ul><h3 id="21d2881f-86f1-8062-b92f-f03e148b19e3" class="">Validation Logic &amp; Error Handling</h3><ul id="21d2881f-86f1-8033-be03-cf9311966fe9" class="bulleted-list"><li style="list-style-type:disc">Inline input validation (field-level)</li></ul><ul id="21d2881f-86f1-8006-afd1-c443a00a3def" class="bulleted-list"><li style="list-style-type:disc">Post-calculation warnings with structured error messages</li></ul><ul id="21d2881f-86f1-8007-820c-ee69981b5cf9" class="bulleted-list"><li style="list-style-type:disc">Custom exceptions (<code>NFPAError</code>, <code>ValidationError</code>)</li></ul><hr id="21d2881f-86f1-8074-a8b9-c21eaa55310a"/><h2 id="21d2881f-86f1-80b8-938a-d4384a73197e" class="">4. UI/UX Specifications</h2><h3 id="21d2881f-86f1-804d-95e9-ed152593226a" class="">Interface Design Approach</h3><ul id="21d2881f-86f1-800e-bd19-c44d703b462f" class="bulleted-list"><li style="list-style-type:disc">Single-page calculator consistent with AirDuctSizer &amp; GreaseDuctSizer</li></ul><ul id="21d2881f-86f1-805b-a7e0-ee68735c666e" class="bulleted-list"><li style="list-style-type:disc">Real-time results panel and dynamic validation feedback</li></ul><ul id="21d2881f-86f1-80b0-8b7e-ed7744f90d15" class="bulleted-list"><li style="list-style-type:disc">Supports SizeWise Suite’s established responsive layout and dark/light themes</li></ul><h3 id="21d2881f-86f1-806e-ae8e-fd135d709e22" class="">Wireframe Recommendations (Textual)</h3><ul id="21d2881f-86f1-8024-bbad-d33a4d8842ee" class="bulleted-list"><li style="list-style-type:disc"><strong>Top section:</strong> Clearly labeled input fields grouped logically (boiler, vent properties)</li></ul><ul id="21d2881f-86f1-80f1-ae5a-fbc17e79d0be" class="bulleted-list"><li style="list-style-type:disc"><strong>Middle section:</strong> Live validation messages displayed next to corresponding inputs</li></ul><ul id="21d2881f-86f1-80e1-8708-f147390cd04f" class="bulleted-list"><li style="list-style-type:disc"><strong>Bottom section:</strong> Real-time calculation results panel, graphical “Draft vs. Height” SVG quick chart</li></ul><hr id="21d2881f-86f1-80d1-b094-d4e43ac442f6"/><h2 id="21d2881f-86f1-80a6-8a95-da31be5af7aa" class="">5. Integration Plan</h2><h3 id="21d2881f-86f1-80b0-bcff-fad8e08e2c30" class="">Ecosystem Integration</h3><ul id="21d2881f-86f1-80d1-b1d9-c65513fe89d5" class="bulleted-list"><li style="list-style-type:disc">Shared validators and unit converters across SizeWise tools</li></ul><ul id="21d2881f-86f1-801b-95f4-d9246fd72149" class="bulleted-list"><li style="list-style-type:disc">Modular structure with toolConfig.json for seamless plug-and-play integration</li></ul><h3 id="21d2881f-86f1-8043-b9e8-e8cb71a98cc6" class="">Data Management</h3><ul id="21d2881f-86f1-8008-9786-c3d770ae4ab6" class="bulleted-list"><li style="list-style-type:disc">Immediate export of results (PDF/CSV)</li></ul><ul id="21d2881f-86f1-80e8-9f2c-cb8592ebb218" class="bulleted-list"><li style="list-style-type:disc">Offline caching using browser storage (localStorage)</li></ul><ul id="21d2881f-86f1-802e-bd43-fc01a2125e84" class="bulleted-list"><li style="list-style-type:disc">Future persistence (SQLite/Postgres, Phase 3.2)</li></ul><hr id="21d2881f-86f1-8072-a631-e25e07e33b9d"/><h2 id="21d2881f-86f1-80d6-b83d-dcdbfb14853e" class="">6. Validation &amp; Standards Compliance</h2><h3 id="21d2881f-86f1-80b4-9a0c-fe4cdc9bea6b" class="">Validation Approach</h3><ul id="21d2881f-86f1-800c-877e-d807b7262d52" class="bulleted-list"><li style="list-style-type:disc">Strict inline validation to prevent invalid user inputs</li></ul><ul id="21d2881f-86f1-801c-a716-e5eb65eac188" class="bulleted-list"><li style="list-style-type:disc">Post-calculation warnings for borderline draft pressures, velocities</li></ul><h3 id="21d2881f-86f1-807a-a96d-d860697690ac" class="">NFPA &amp; IFGC Compliance Logic</h3><ul id="21d2881f-86f1-80a3-bcb0-cbe5c390480b" class="bulleted-list"><li style="list-style-type:disc">NFPA 54 as primary validation standard</li></ul><ul id="21d2881f-86f1-8052-813e-e299946239d0" class="bulleted-list"><li style="list-style-type:disc">Secondary IFGC warnings clearly indicated but not enforced</li></ul><ul id="21d2881f-86f1-8030-aab2-e8652d2c3024" class="bulleted-list"><li style="list-style-type:disc">Centralized standards_validation module references NFPA rules from JSON/YAML constants</li></ul><hr id="21d2881f-86f1-8084-902c-db16c856ec0a"/><h2 id="21d2881f-86f1-80ee-8f02-e052cc975a8b" class="">7. Implementation Roadmap</h2><h3 id="21d2881f-86f1-8051-b84e-c75b037be59c" class="">Task-Level Breakdown</h3><ul id="21d2881f-86f1-8070-b307-d0276d0161b5" class="bulleted-list"><li style="list-style-type:disc"><strong>Sprint 1:</strong> Setup folders, define schemas, constants, and initial formulas</li></ul><ul id="21d2881f-86f1-800a-955e-e72a08eb30e1" class="bulleted-list"><li style="list-style-type:disc"><strong>Sprint 2:</strong> Implement core calculation logic, initial UI, API endpoints</li></ul><ul id="21d2881f-86f1-80d3-b5d7-fe0c85b7572a" class="bulleted-list"><li style="list-style-type:disc"><strong>Sprint 3:</strong> Integrate inline validation, NFPA compliance logic</li></ul><ul id="21d2881f-86f1-808a-962b-d3afb5e287f1" class="bulleted-list"><li style="list-style-type:disc"><strong>Sprint 4:</strong> Add PDF/CSV export functionality, SVG graph, complete unit and integration tests</li></ul><h3 id="21d2881f-86f1-80b5-83ea-f3cb2d9e7690" class="">Timeline and Dependencies</h3><ul id="21d2881f-86f1-802f-98b5-d36ee6003f13" class="bulleted-list"><li style="list-style-type:disc">Total timeline: 4 sprints (2 weeks per sprint)</li></ul><ul id="21d2881f-86f1-8046-9bf9-ed859922222b" class="bulleted-list"><li style="list-style-type:disc">Begins immediately after Grease Duct Sizer MVP completion</li></ul><ul id="21d2881f-86f1-802b-ba44-f54f0cf41afd" class="bulleted-list"><li style="list-style-type:disc">Dependencies: Existing SizeWise tool framework, completion of Grease Duct Sizer</li></ul><hr id="21d2881f-86f1-808d-9221-dd1df3f58243"/><h2 id="21d2881f-86f1-80e1-9438-d9f8c02abe29" class="">8. Documentation Plan</h2><h3 id="21d2881f-86f1-8079-a5bf-dad755094904" class="">Internal Code Documentation</h3><ul id="21d2881f-86f1-8051-a5ba-fe20ad89d4bf" class="bulleted-list"><li style="list-style-type:disc">Comprehensive inline documentation for functions and modules</li></ul><ul id="21d2881f-86f1-80ad-af68-ed6575d520cc" class="bulleted-list"><li style="list-style-type:disc">Auto-generated API documentation (Sphinx or MkDocs)</li></ul><h3 id="21d2881f-86f1-8087-ace1-fbe432a255fe" class="">User Documentation</h3><ul id="21d2881f-86f1-8012-9966-f68bf3da2cc6" class="bulleted-list"><li style="list-style-type:disc">User-facing help page clearly describing inputs, outputs, NFPA compliance logic</li></ul><ul id="21d2881f-86f1-8083-8896-f4b5841186af" class="bulleted-list"><li style="list-style-type:disc">Example usage scenarios provided</li></ul><hr id="21d2881f-86f1-80c2-9775-e011730b3216"/><h2 id="21d2881f-86f1-801b-a729-f16f123d692c" class="">9. Testing Plan</h2><h3 id="21d2881f-86f1-8007-a3ad-c24a3d1dbad3" class="">Unit, Integration, E2E Testing</h3><ul id="21d2881f-86f1-80ff-96d4-ec5fd97d6ad6" class="bulleted-list"><li style="list-style-type:disc">Unit tests: Jest (for React components and TypeScript logic)</li></ul><ul id="21d2881f-86f1-806d-b3f9-f362b2f97411" class="bulleted-list"><li style="list-style-type:disc">Integration/E2E tests: Playwright for UI validation</li></ul><h3 id="21d2881f-86f1-8086-9ada-d3ae23f05c95" class="">Automation &amp; CI/CD</h3><ul id="21d2881f-86f1-807a-886c-c6841272cf02" class="bulleted-list"><li style="list-style-type:disc">Automated testing configured via GitHub Actions</li></ul><ul id="21d2881f-86f1-8036-b7bf-fe6f488e15ee" class="bulleted-list"><li style="list-style-type:disc">Pipeline includes build verification, unit/integration test execution, and deployment automation</li></ul><hr id="21d2881f-86f1-803b-857e-ee4a7597f3cc"/><h2 id="21d2881f-86f1-80ba-aca3-d5311a4075ad" class="">10. Risk &amp; Mitigation Plan</h2><h3 id="21d2881f-86f1-8050-8db2-d97c8c581f3f" class="">Potential Risks</h3><ol type="1" id="21d2881f-86f1-80e9-834e-df4b01b3fb26" class="numbered-list" start="1"><li><strong>NFPA standard changes</strong><ul id="21d2881f-86f1-80ca-9c4d-cb9003b258fd" class="bulleted-list"><li style="list-style-type:disc">Mitigation: Modular rules file easily updated without affecting core logic</li></ul></li></ol><ol type="1" id="21d2881f-86f1-80aa-8fb0-d8f4e047c3d0" class="numbered-list" start="2"><li><strong>Frontend/Backend Integration Delays</strong><ul id="21d2881f-86f1-8025-a71c-c5dc5bda903e" class="bulleted-list"><li style="list-style-type:disc">Mitigation: Clearly defined API schema; interim mock API responses</li></ul></li></ol><ol type="1" id="21d2881f-86f1-80ba-b7c1-f8f8446b00c8" class="numbered-list" start="3"><li><strong>Validation Logic Complexity</strong><ul id="21d2881f-86f1-8058-a9c4-efaa6e8e2447" class="bulleted-list"><li style="list-style-type:disc">Mitigation: Structured and modular validation modules, detailed unit tests</li></ul></li></ol><ol type="1" id="21d2881f-86f1-80a5-a246-fa2d7864c57f" class="numbered-list" start="4"><li><strong>Offline Mode Limitations</strong><ul id="21d2881f-86f1-80ae-be99-e0365b0c15e9" class="bulleted-list"><li style="list-style-type:disc">Mitigation: Thorough offline capability testing with clearly documented limitations</li></ul></li></ol><ol type="1" id="21d2881f-86f1-80e8-a46e-e2eedc4e5859" class="numbered-list" start="5"><li><strong>Timeline Slippage</strong><ul id="21d2881f-86f1-804a-8de3-e2258b4c3d5d" class="bulleted-list"><li style="list-style-type:disc">Mitigation: Regular sprint reviews, clear milestone tracking, buffer for unexpected delays</li></ul></li></ol><hr id="21d2881f-86f1-80fd-95a4-fc8b50afa7e7"/><p id="21d2881f-86f1-80c6-9cdb-c4e75220b9ff" class="">This comprehensive planning document provides a clear implementation strategy ensuring efficient, accurate delivery of the Boiler Vent Sizer tool within your SizeWise Suite.</p></div></article><span class="sans" style="font-size:14px;padding-top:2em"></span></body></html>