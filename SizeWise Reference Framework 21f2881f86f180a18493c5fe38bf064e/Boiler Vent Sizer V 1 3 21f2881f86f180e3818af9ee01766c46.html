<html><head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"/><title>Boiler Vent Sizer V 1.3</title><style>
/* cspell:disable-file */
/* webkit printing magic: print all background colors */
html {
	-webkit-print-color-adjust: exact;
}
* {
	box-sizing: border-box;
	-webkit-print-color-adjust: exact;
}

html,
body {
	margin: 0;
	padding: 0;
}
@media only screen {
	body {
		margin: 2em auto;
		max-width: 900px;
		color: rgb(55, 53, 47);
	}
}

body {
	line-height: 1.5;
	white-space: pre-wrap;
}

a,
a.visited {
	color: inherit;
	text-decoration: underline;
}

.pdf-relative-link-path {
	font-size: 80%;
	color: #444;
}

h1,
h2,
h3 {
	letter-spacing: -0.01em;
	line-height: 1.2;
	font-weight: 600;
	margin-bottom: 0;
}

.page-title {
	font-size: 2.5rem;
	font-weight: 700;
	margin-top: 0;
	margin-bottom: 0.75em;
}

h1 {
	font-size: 1.875rem;
	margin-top: 1.875rem;
}

h2 {
	font-size: 1.5rem;
	margin-top: 1.5rem;
}

h3 {
	font-size: 1.25rem;
	margin-top: 1.25rem;
}

.source {
	border: 1px solid #ddd;
	border-radius: 3px;
	padding: 1.5em;
	word-break: break-all;
}

.callout {
	border-radius: 3px;
	padding: 1rem;
}

figure {
	margin: 1.25em 0;
	page-break-inside: avoid;
}

figcaption {
	opacity: 0.5;
	font-size: 85%;
	margin-top: 0.5em;
}

mark {
	background-color: transparent;
}

.indented {
	padding-left: 1.5em;
}

hr {
	background: transparent;
	display: block;
	width: 100%;
	height: 1px;
	visibility: visible;
	border: none;
	border-bottom: 1px solid rgba(55, 53, 47, 0.09);
}

img {
	max-width: 100%;
}

@media only print {
	img {
		max-height: 100vh;
		object-fit: contain;
	}
}

@page {
	margin: 1in;
}

.collection-content {
	font-size: 0.875rem;
}

.column-list {
	display: flex;
	justify-content: space-between;
}

.column {
	padding: 0 1em;
}

.column:first-child {
	padding-left: 0;
}

.column:last-child {
	padding-right: 0;
}

.table_of_contents-item {
	display: block;
	font-size: 0.875rem;
	line-height: 1.3;
	padding: 0.125rem;
}

.table_of_contents-indent-1 {
	margin-left: 1.5rem;
}

.table_of_contents-indent-2 {
	margin-left: 3rem;
}

.table_of_contents-indent-3 {
	margin-left: 4.5rem;
}

.table_of_contents-link {
	text-decoration: none;
	opacity: 0.7;
	border-bottom: 1px solid rgba(55, 53, 47, 0.18);
}

table,
th,
td {
	border: 1px solid rgba(55, 53, 47, 0.09);
	border-collapse: collapse;
}

table {
	border-left: none;
	border-right: none;
}

th,
td {
	font-weight: normal;
	padding: 0.25em 0.5em;
	line-height: 1.5;
	min-height: 1.5em;
	text-align: left;
}

th {
	color: rgba(55, 53, 47, 0.6);
}

ol,
ul {
	margin: 0;
	margin-block-start: 0.6em;
	margin-block-end: 0.6em;
}

li > ol:first-child,
li > ul:first-child {
	margin-block-start: 0.6em;
}

ul > li {
	list-style: disc;
}

ul.to-do-list {
	padding-inline-start: 0;
}

ul.to-do-list > li {
	list-style: none;
}

.to-do-children-checked {
	text-decoration: line-through;
	opacity: 0.375;
}

ul.toggle > li {
	list-style: none;
}

ul {
	padding-inline-start: 1.7em;
}

ul > li {
	padding-left: 0.1em;
}

ol {
	padding-inline-start: 1.6em;
}

ol > li {
	padding-left: 0.2em;
}

.mono ol {
	padding-inline-start: 2em;
}

.mono ol > li {
	text-indent: -0.4em;
}

.toggle {
	padding-inline-start: 0em;
	list-style-type: none;
}

/* Indent toggle children */
.toggle > li > details {
	padding-left: 1.7em;
}

.toggle > li > details > summary {
	margin-left: -1.1em;
}

.selected-value {
	display: inline-block;
	padding: 0 0.5em;
	background: rgba(206, 205, 202, 0.5);
	border-radius: 3px;
	margin-right: 0.5em;
	margin-top: 0.3em;
	margin-bottom: 0.3em;
	white-space: nowrap;
}

.collection-title {
	display: inline-block;
	margin-right: 1em;
}

.page-description {
	margin-bottom: 2em;
}

.simple-table {
	margin-top: 1em;
	font-size: 0.875rem;
	empty-cells: show;
}
.simple-table td {
	height: 29px;
	min-width: 120px;
}

.simple-table th {
	height: 29px;
	min-width: 120px;
}

.simple-table-header-color {
	background: rgb(247, 246, 243);
	color: black;
}
.simple-table-header {
	font-weight: 500;
}

time {
	opacity: 0.5;
}

.icon {
	display: inline-block;
	max-width: 1.2em;
	max-height: 1.2em;
	text-decoration: none;
	vertical-align: text-bottom;
	margin-right: 0.5em;
}

img.icon {
	border-radius: 3px;
}

.user-icon {
	width: 1.5em;
	height: 1.5em;
	border-radius: 100%;
	margin-right: 0.5rem;
}

.user-icon-inner {
	font-size: 0.8em;
}

.text-icon {
	border: 1px solid #000;
	text-align: center;
}

.page-cover-image {
	display: block;
	object-fit: cover;
	width: 100%;
	max-height: 30vh;
}

.page-header-icon {
	font-size: 3rem;
	margin-bottom: 1rem;
}

.page-header-icon-with-cover {
	margin-top: -0.72em;
	margin-left: 0.07em;
}

.page-header-icon img {
	border-radius: 3px;
}

.link-to-page {
	margin: 1em 0;
	padding: 0;
	border: none;
	font-weight: 500;
}

p > .user {
	opacity: 0.5;
}

td > .user,
td > time {
	white-space: nowrap;
}

input[type="checkbox"] {
	transform: scale(1.5);
	margin-right: 0.6em;
	vertical-align: middle;
}

p {
	margin-top: 0.5em;
	margin-bottom: 0.5em;
}

.image {
	border: none;
	margin: 1.5em 0;
	padding: 0;
	border-radius: 0;
	text-align: center;
}

.code,
code {
	background: rgba(135, 131, 120, 0.15);
	border-radius: 3px;
	padding: 0.2em 0.4em;
	border-radius: 3px;
	font-size: 85%;
	tab-size: 2;
}

code {
	color: #eb5757;
}

.code {
	padding: 1.5em 1em;
}

.code-wrap {
	white-space: pre-wrap;
	word-break: break-all;
}

.code > code {
	background: none;
	padding: 0;
	font-size: 100%;
	color: inherit;
}

blockquote {
	font-size: 1.25em;
	margin: 1em 0;
	padding-left: 1em;
	border-left: 3px solid rgb(55, 53, 47);
}

.bookmark {
	text-decoration: none;
	max-height: 8em;
	padding: 0;
	display: flex;
	width: 100%;
	align-items: stretch;
}

.bookmark-title {
	font-size: 0.85em;
	overflow: hidden;
	text-overflow: ellipsis;
	height: 1.75em;
	white-space: nowrap;
}

.bookmark-text {
	display: flex;
	flex-direction: column;
}

.bookmark-info {
	flex: 4 1 180px;
	padding: 12px 14px 14px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.bookmark-image {
	width: 33%;
	flex: 1 1 180px;
	display: block;
	position: relative;
	object-fit: cover;
	border-radius: 1px;
}

.bookmark-description {
	color: rgba(55, 53, 47, 0.6);
	font-size: 0.75em;
	overflow: hidden;
	max-height: 4.5em;
	word-break: break-word;
}

.bookmark-href {
	font-size: 0.75em;
	margin-top: 0.25em;
}

.sans { font-family: ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol"; }
.code { font-family: "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace; }
.serif { font-family: Lyon-Text, Georgia, ui-serif, serif; }
.mono { font-family: iawriter-mono, Nitti, Menlo, Courier, monospace; }
.pdf .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK JP'; }
.pdf:lang(zh-CN) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK SC'; }
.pdf:lang(zh-TW) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK TC'; }
.pdf:lang(ko-KR) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK KR'; }
.pdf .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK JP'; }
.pdf:lang(zh-CN) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC'; }
.pdf:lang(zh-TW) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK TC'; }
.pdf:lang(ko-KR) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK KR'; }
.pdf .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK JP'; }
.pdf:lang(zh-CN) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK SC'; }
.pdf:lang(zh-TW) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK TC'; }
.pdf:lang(ko-KR) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK KR'; }
.pdf .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK JP'; }
.pdf:lang(zh-CN) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC'; }
.pdf:lang(zh-TW) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK TC'; }
.pdf:lang(ko-KR) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK KR'; }
.highlight-default {
	color: rgba(50, 48, 44, 1);
}
.highlight-gray {
	color: rgba(115, 114, 110, 1);
	fill: rgba(115, 114, 110, 1);
}
.highlight-brown {
	color: rgba(159, 107, 83, 1);
	fill: rgba(159, 107, 83, 1);
}
.highlight-orange {
	color: rgba(217, 115, 13, 1);
	fill: rgba(217, 115, 13, 1);
}
.highlight-yellow {
	color: rgba(203, 145, 47, 1);
	fill: rgba(203, 145, 47, 1);
}
.highlight-teal {
	color: rgba(68, 131, 97, 1);
	fill: rgba(68, 131, 97, 1);
}
.highlight-blue {
	color: rgba(51, 126, 169, 1);
	fill: rgba(51, 126, 169, 1);
}
.highlight-purple {
	color: rgba(144, 101, 176, 1);
	fill: rgba(144, 101, 176, 1);
}
.highlight-pink {
	color: rgba(193, 76, 138, 1);
	fill: rgba(193, 76, 138, 1);
}
.highlight-red {
	color: rgba(205, 60, 58, 1);
	fill: rgba(205, 60, 58, 1);
}
.highlight-default_background {
	color: rgba(50, 48, 44, 1);
}
.highlight-gray_background {
	background: rgba(248, 248, 247, 1);
}
.highlight-brown_background {
	background: rgba(244, 238, 238, 1);
}
.highlight-orange_background {
	background: rgba(251, 236, 221, 1);
}
.highlight-yellow_background {
	background: rgba(251, 243, 219, 1);
}
.highlight-teal_background {
	background: rgba(237, 243, 236, 1);
}
.highlight-blue_background {
	background: rgba(231, 243, 248, 1);
}
.highlight-purple_background {
	background: rgba(248, 243, 252, 1);
}
.highlight-pink_background {
	background: rgba(252, 241, 246, 1);
}
.highlight-red_background {
	background: rgba(253, 235, 236, 1);
}
.block-color-default {
	color: inherit;
	fill: inherit;
}
.block-color-gray {
	color: rgba(115, 114, 110, 1);
	fill: rgba(115, 114, 110, 1);
}
.block-color-brown {
	color: rgba(159, 107, 83, 1);
	fill: rgba(159, 107, 83, 1);
}
.block-color-orange {
	color: rgba(217, 115, 13, 1);
	fill: rgba(217, 115, 13, 1);
}
.block-color-yellow {
	color: rgba(203, 145, 47, 1);
	fill: rgba(203, 145, 47, 1);
}
.block-color-teal {
	color: rgba(68, 131, 97, 1);
	fill: rgba(68, 131, 97, 1);
}
.block-color-blue {
	color: rgba(51, 126, 169, 1);
	fill: rgba(51, 126, 169, 1);
}
.block-color-purple {
	color: rgba(144, 101, 176, 1);
	fill: rgba(144, 101, 176, 1);
}
.block-color-pink {
	color: rgba(193, 76, 138, 1);
	fill: rgba(193, 76, 138, 1);
}
.block-color-red {
	color: rgba(205, 60, 58, 1);
	fill: rgba(205, 60, 58, 1);
}
.block-color-default_background {
	color: inherit;
	fill: inherit;
}
.block-color-gray_background {
	background: rgba(248, 248, 247, 1);
}
.block-color-brown_background {
	background: rgba(244, 238, 238, 1);
}
.block-color-orange_background {
	background: rgba(251, 236, 221, 1);
}
.block-color-yellow_background {
	background: rgba(251, 243, 219, 1);
}
.block-color-teal_background {
	background: rgba(237, 243, 236, 1);
}
.block-color-blue_background {
	background: rgba(231, 243, 248, 1);
}
.block-color-purple_background {
	background: rgba(248, 243, 252, 1);
}
.block-color-pink_background {
	background: rgba(252, 241, 246, 1);
}
.block-color-red_background {
	background: rgba(253, 235, 236, 1);
}
.select-value-color-default { background-color: rgba(84, 72, 49, 0.08); }
.select-value-color-gray { background-color: rgba(84, 72, 49, 0.15); }
.select-value-color-brown { background-color: rgba(210, 162, 141, 0.35); }
.select-value-color-orange { background-color: rgba(224, 124, 57, 0.27); }
.select-value-color-yellow { background-color: rgba(236, 191, 66, 0.39); }
.select-value-color-green { background-color: rgba(123, 183, 129, 0.27); }
.select-value-color-blue { background-color: rgba(93, 165, 206, 0.27); }
.select-value-color-purple { background-color: rgba(168, 129, 197, 0.27); }
.select-value-color-pink { background-color: rgba(225, 136, 179, 0.27); }
.select-value-color-red { background-color: rgba(244, 171, 159, 0.4); }

.checkbox {
	display: inline-flex;
	vertical-align: text-bottom;
	width: 16;
	height: 16;
	background-size: 16px;
	margin-left: 2px;
	margin-right: 5px;
}

.checkbox-on {
	background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20width%3D%2216%22%20height%3D%2216%22%20fill%3D%22%2358A9D7%22%2F%3E%0A%3Cpath%20d%3D%22M6.71429%2012.2852L14%204.9995L12.7143%203.71436L6.71429%209.71378L3.28571%206.2831L2%207.57092L6.71429%2012.2852Z%22%20fill%3D%22white%22%2F%3E%0A%3C%2Fsvg%3E");
}

.checkbox-off {
	background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20x%3D%220.75%22%20y%3D%220.75%22%20width%3D%2214.5%22%20height%3D%2214.5%22%20fill%3D%22white%22%20stroke%3D%22%2336352F%22%20stroke-width%3D%221.5%22%2F%3E%0A%3C%2Fsvg%3E");
}
	
</style></head><body><article id="21f2881f-86f1-80e3-818a-f9ee01766c46" class="page sans"><header><div class="page-header-icon undefined"><span class="icon">📄</span></div><h1 class="page-title">Boiler Vent Sizer V 1.3</h1><p class="page-description"></p></header><div class="page-body"><h1 id="21f2881f-86f1-803a-b9ef-d5fa174d12ea" class="">Boiler Vent Sizer</h1><blockquote id="21f2881f-86f1-806c-b60a-ed7963b3dcb6" class="">Document Structure Reference (matches SizeWise master outline)<table id="21f2881f-86f1-807d-93d9-e634699273b3" class="simple-table"><tbody><tr id="21f2881f-86f1-804e-a2fb-d5198db7c75e"><td id="MW^N" class="">#</td><td id="?TEE" class="">Section</td></tr><tr id="21f2881f-86f1-8054-be79-dbfe055691b1"><td id="MW^N" class=""><strong>1</strong></td><td id="?TEE" class="">Project Overview</td></tr><tr id="21f2881f-86f1-8060-83c8-c07fab05d183"><td id="MW^N" class=""><strong>2</strong></td><td id="?TEE" class="">Functional Requirements</td></tr><tr id="21f2881f-86f1-8041-a946-ff95cee68935"><td id="MW^N" class=""><strong>3</strong></td><td id="?TEE" class="">Technical Architecture</td></tr><tr id="21f2881f-86f1-80cd-a678-f8378eb0b4ba"><td id="MW^N" class=""><strong>4</strong></td><td id="?TEE" class="">Integration Plan</td></tr><tr id="21f2881f-86f1-8047-9b93-f6d305224980"><td id="MW^N" class=""><strong>5</strong></td><td id="?TEE" class="">UI / UX Specifications</td></tr><tr id="21f2881f-86f1-8040-bc27-c86d19ee07a4"><td id="MW^N" class=""><strong>6</strong></td><td id="?TEE" class="">Validation &amp; Standards Compliance</td></tr><tr id="21f2881f-86f1-8093-ae1f-cdda6e528e69"><td id="MW^N" class=""><strong>7</strong></td><td id="?TEE" class="">Performance &amp; Scalability</td></tr><tr id="21f2881f-86f1-80ef-879c-f65eccbe5a97"><td id="MW^N" class=""><strong>8</strong></td><td id="?TEE" class="">Security, Privacy &amp; Compliance</td></tr><tr id="************************************"><td id="MW^N" class=""><strong>9</strong></td><td id="?TEE" class="">Accessibility &amp; Internationalization</td></tr><tr id="21f2881f-86f1-80c2-8fe6-e9690884e0b3"><td id="MW^N" class=""><strong>10</strong></td><td id="?TEE" class="">Deployment &amp; Release Management</td></tr><tr id="21f2881f-86f1-80d2-a35b-feb5c3f58c7f"><td id="MW^N" class=""><strong>11</strong></td><td id="?TEE" class="">Implementation Roadmap</td></tr><tr id="21f2881f-86f1-80cb-8340-f20936ace1fe"><td id="MW^N" class=""><strong>12</strong></td><td id="?TEE" class="">Testing Plan</td></tr><tr id="21f2881f-86f1-8008-954a-f5b95ca8c241"><td id="MW^N" class=""><strong>13</strong></td><td id="?TEE" class="">Documentation Strategy</td></tr><tr id="21f2881f-86f1-804e-a696-ff04a95d3f7a"><td id="MW^N" class=""><strong>14</strong></td><td id="?TEE" class="">Risk Assessment &amp; Mitigation</td></tr><tr id="21f2881f-86f1-809e-829b-c7a8985c2c9d"><td id="MW^N" class=""><strong>15</strong></td><td id="?TEE" class="">Maintenance &amp; Support Plan</td></tr><tr id="21f2881f-86f1-8080-b9ca-f80b33c1a080"><td id="MW^N" class=""><strong>16</strong></td><td id="?TEE" class="">Appendices</td></tr></tbody></table></blockquote><hr id="21f2881f-86f1-8050-bae5-c09278e62458"/><h2 id="21f2881f-86f1-8074-b2b7-fd4f1e7a2c53" class="">1. Project Overview - Planning Document</h2><hr id="21f2881f-86f1-801d-b170-d06ef66fd372"/><h2 id="21f2881f-86f1-809f-9f72-e1755d8d1a23" class="">1. Overview &amp; Goals</h2><h3 id="21f2881f-86f1-8009-b21a-ec9a52a073ef" class="">Purpose and Objectives</h3><ul id="21f2881f-86f1-803b-852d-cfbba6a5b949" class="bulleted-list"><li style="list-style-type:disc">Implement the &quot;Boiler Vent Sizer&quot; tool as a high-priority feature within the SizeWise Suite.</li></ul><ul id="21f2881f-86f1-8008-a241-caeb90e4b329" class="bulleted-list"><li style="list-style-type:disc">Provide HVAC engineers and estimators with a streamlined calculator for vent sizing compliant with NFPA 54 standards.</li></ul><ul id="21f2881f-86f1-807e-9b33-e67edab43ebe" class="bulleted-list"><li style="list-style-type:disc">Facilitate rapid and accurate sizing decisions, minimizing calculation errors and ensuring compliance.</li></ul><h3 id="21f2881f-86f1-8090-9f68-cb5f0906b383" class="">Problem Statement &amp; User Benefits</h3><ul id="21f2881f-86f1-8031-945e-e907fe1a220c" class="bulleted-list"><li style="list-style-type:disc">Users currently lack a consolidated, standards-compliant boiler vent sizing tool.</li></ul><ul id="21f2881f-86f1-80d7-a4db-c4d954ac25ca" class="bulleted-list"><li style="list-style-type:disc">Expected benefits include increased productivity, reduced compliance errors, and streamlined workflow with immediate results and validations.</li></ul><hr id="21f2881f-86f1-80be-92ed-e5c2e8b2a93b"/><h2 id="21f2881f-86f1-8096-87bb-c5dd0a48eaa8" class="">2. Functional Requirements</h2><h3 id="21f2881f-86f1-80d7-b09b-c82a7c8be502" class="">Inputs</h3><ul id="21f2881f-86f1-808e-818a-f4a90fa10050" class="bulleted-list"><li style="list-style-type:disc">Boiler capacity (MBH/kW)</li></ul><ul id="21f2881f-86f1-802a-8077-ca9db355251b" class="bulleted-list"><li style="list-style-type:disc">Turn-down ratio (optional)</li></ul><ul id="21f2881f-86f1-80d4-ac39-e1d6f6fb295e" class="bulleted-list"><li style="list-style-type:disc">Fuel type (Natural Gas, Propane, Oil)</li></ul><ul id="21f2881f-86f1-8081-bd82-fce3a190405e" class="bulleted-list"><li style="list-style-type:disc">Flue gas temperature, ambient temperature, altitude</li></ul><ul id="21f2881f-86f1-805b-97e4-d46ee8b8c49f" class="bulleted-list"><li style="list-style-type:disc">Vent material (Type B, Stainless Steel, etc.)</li></ul><ul id="21f2881f-86f1-8071-b01e-c63758f99d1c" class="bulleted-list"><li style="list-style-type:disc">System geometry: total length, vertical height, elbows (number and equivalent length), pitch</li></ul><h3 id="21f2881f-86f1-80f2-86f1-f3e7f22be0a7" class="">Outputs</h3><ul id="21f2881f-86f1-8096-b0cd-fdac74af3d4a" class="bulleted-list"><li style="list-style-type:disc">Required vent diameter</li></ul><ul id="21f2881f-86f1-806d-9586-df37877a6efd" class="bulleted-list"><li style="list-style-type:disc">Draft pressure (+/- in. WC) at appliance and termination</li></ul><ul id="21f2881f-86f1-8028-8352-fc95fbe77365" class="bulleted-list"><li style="list-style-type:disc">Flue gas velocity and temperature at exit</li></ul><ul id="21f2881f-86f1-804d-91c2-d6a5af76d935" class="bulleted-list"><li style="list-style-type:disc">Combustion air requirement (CFM)</li></ul><ul id="21f2881f-86f1-80c6-b74c-d7a46f8d5239" class="bulleted-list"><li style="list-style-type:disc">NFPA Category (I-IV) determination</li></ul><ul id="21f2881f-86f1-80f7-b69a-e435024ef150" class="bulleted-list"><li style="list-style-type:disc">Compliance validation with NFPA 54; secondary IFGC warnings</li></ul><hr id="21f2881f-86f1-8064-a025-ee2f987a96dd"/><h2 id="21f2881f-86f1-80b1-a717-f18536c950ac" class="">3. Technical Architecture</h2><h3 id="21f2881f-86f1-80b1-9f5e-e2c4fe86160a" class="">3.1 Folder &amp; Module Structure (Hybrid‑Ready)</h3><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="21f2881f-86f1-803b-9985-c2d4ab390d6a" class="code"><code class="language-Plain Text" style="white-space:pre-wrap;word-break:break-all">app/
├─ tools/
│   └─ boiler_venting/
│       ├─ canvas/                    # React‑Konva components &amp; hooks
│       │   └─ VentCanvas.tsx
│       ├─ solver/                    # Hybrid sizing engine (runs in WebWorker)
│       │   ├─ networkParser.ts       # Pixel → real‑units translation
│       │   ├─ tableEngine.ts         # NFPA 54 lookup path
│       │   ├─ physicsEngine.ts       # Darcy‑Weisbach + temp loop
│       │   ├─ draftWorker.ts         # WebWorker wrapper
│       │   └─ convergence.ts         # Shared utility fns
│       ├─ validation/
│       │   ├─ nfpaRules.json         # Code data tables
│       │   └─ validator.ts           # Category flip, condensation check
│       ├─ types/
│       │   └─ ventNetwork.d.ts       # TS types auto‑generated from JSON schema
│       ├─ schemas/
│       │   └─ ventNetwork.schema.json
│       ├─ services/                  # Export helpers
│       │   ├─ exportPdf.ts
│       │   └─ exportCsv.ts
│       ├─ constants.ts
│       ├─ ui.tsx                     # Wrapper for canvas + results panel
│       ├─ api.ts                     # Bridge for future Flask backend
│       └─ tests/
│           └─ (unit + integration)
</code></pre><h3 id="21f2881f-86f1-8002-8e91-eafb6f36c0b2" class="">3.2 Canvas ↔ Solver Data Contract</h3><ul id="21f2881f-86f1-807d-9125-cd0f148c1a10" class="bulleted-list"><li style="list-style-type:disc"><strong>Pixel Coordinates</strong> – every node has <code>position: [x_px, y_px]</code>.</li></ul><ul id="21f2881f-86f1-804f-8f3b-c4b9a4bf161b" class="bulleted-list"><li style="list-style-type:disc"><strong>scaleFactor (number)</strong> – converts pixels → real‑world units in the current project (e.g. <em>25 px = 1 ft</em>).</li></ul><ul id="21f2881f-86f1-80aa-bb4d-e349ef1b9e56" class="bulleted-list"><li style="list-style-type:disc"><strong>units (enum)</strong> – <code>imperial</code> <em>(default)</em> or <code>metric</code>; governs all physical fields.</li></ul><ul id="21f2881f-86f1-8074-93e7-c789d9b29a4d" class="bulleted-list"><li style="list-style-type:disc"><strong>schemaVersion (&quot;1.0.0&quot;)</strong> – allows future migrations.</li></ul><ul id="21f2881f-86f1-8071-ac18-c5b64beb188d" class="bulleted-list"><li style="list-style-type:disc"><strong>Max items</strong> – <code>maxNodes</code> ≤ 100, <code>maxSegments</code> ≤ 100 (enforced by schema &amp; UI).</li></ul><ul id="21f2881f-86f1-8042-9e03-ce44917831e2" class="bulleted-list"><li style="list-style-type:disc"><strong>Soft validation</strong> – unknown keys logged but not fatal.</li></ul><p id="21f2881f-86f1-800b-a493-fa40589c7b99" class="">Example stub (imperial):</p><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="21f2881f-86f1-8014-a604-ebc7586dfda6" class="code"><code class="language-JSON" style="white-space:pre-wrap;word-break:break-all">{
  &quot;schemaVersion&quot;: &quot;1.0.0&quot;,
  &quot;units&quot;: &quot;imperial&quot;,
  &quot;scaleFactor&quot;: 25,
  &quot;nodes&quot;: [
    { &quot;id&quot;: &quot;n1&quot;, &quot;type&quot;: &quot;boiler&quot;, &quot;position&quot;: [100,200] },
    { &quot;id&quot;: &quot;n2&quot;, &quot;type&quot;: &quot;elbow_90&quot;, &quot;position&quot;: [250,200] },
    { &quot;id&quot;: &quot;n3&quot;, &quot;type&quot;: &quot;termination&quot;, &quot;terminationType&quot;: &quot;rain_cap&quot;, &quot;position&quot;: [400,100] }
  ],
  &quot;segments&quot;: [
    { &quot;id&quot;: &quot;s1&quot;, &quot;from&quot;: &quot;n1&quot;, &quot;to&quot;: &quot;n2&quot;, &quot;length&quot;: 6, &quot;diameter&quot;: 4, &quot;material&quot;: &quot;TypeB&quot;, &quot;insulation&quot;: { &quot;thickness&quot;: 1, &quot;density&quot;: 35 }, &quot;pitch&quot;: 0.25 },
    { &quot;id&quot;: &quot;s2&quot;, &quot;from&quot;: &quot;n2&quot;, &quot;to&quot;: &quot;n3&quot;, &quot;length&quot;: 12, &quot;diameter&quot;: 4, &quot;material&quot;: &quot;TypeB&quot;, &quot;insulation&quot;: { &quot;thickness&quot;: 1, &quot;density&quot;: 35 }, &quot;pitch&quot;: 0.25 }
  ]
}
</code></pre><h3 id="21f2881f-86f1-80e8-9f5a-e3739b006cfd" class="">3.3 API Endpoints (future Flask backend)</h3><table id="21f2881f-86f1-80eb-b015-f35239a94fac" class="simple-table"><tbody><tr id="21f2881f-86f1-80a2-ae10-ee4374dce026"><td id="Ypc{" class="">Endpoint</td><td id="gTE|" class="">Method</td><td id="MZV}" class="">Purpose</td></tr><tr id="21f2881f-86f1-8053-9a9a-f4c7859dac9e"><td id="Ypc{" class=""><code>/calculate</code></td><td id="gTE|" class="">POST</td><td id="MZV}" class="">Accepts validated ventNetwork JSON; returns per‑segment ΔP, velocity, temp, NFPA category, warnings.</td></tr><tr id="21f2881f-86f1-8009-b052-c453fce90fd1"><td id="Ypc{" class=""><code>/export/pdf</code></td><td id="gTE|" class="">POST</td><td id="MZV}" class="">Receives network + results → returns PDF blob</td></tr></tbody></table><h3 id="21f2881f-86f1-8011-905b-da32dbcd1830" class="">3.4 Validation &amp; Error Handling</h3><ul id="21f2881f-86f1-8045-9b55-c4734c41ae26" class="bulleted-list"><li style="list-style-type:disc"><strong>AJV soft‑mode</strong> in front‑end validates JSON; warnings surface in UI.</li></ul><ul id="21f2881f-86f1-80cb-84a5-d05c6ec2bf63" class="bulleted-list"><li style="list-style-type:disc">Solver re‑validates; if <code>maxSegments</code> exceeded ⇒ returns <code>NetworkTooLargeError</code>.</li></ul><ul id="21f2881f-86f1-8022-8f4c-ea34aa9b99e7" class="bulleted-list"><li style="list-style-type:disc">Category flip, condensation risk, material mismatch raise structured warnings attached to each segment result.</li></ul><h2 id="21f2881f-86f1-803a-a51a-e143bebf10e0" class="">4. UI/UX Specifications. UI/UX Specifications</h2><h3 id="21f2881f-86f1-802c-9663-db236a299bb0" class="">Interface Design Approach</h3><ul id="21f2881f-86f1-80a4-b064-eefebe7a47e5" class="bulleted-list"><li style="list-style-type:disc">Single-page calculator consistent with AirDuctSizer &amp; GreaseDuctSizer</li></ul><ul id="21f2881f-86f1-80c0-8bcb-fe0b8ffed502" class="bulleted-list"><li style="list-style-type:disc">Real-time results panel and dynamic validation feedback</li></ul><ul id="21f2881f-86f1-8035-9ddd-e674953fecbc" class="bulleted-list"><li style="list-style-type:disc">Supports SizeWise Suite’s established responsive layout and dark/light themes</li></ul><h3 id="21f2881f-86f1-804e-88dd-d5587157a4d1" class="">Wireframe Recommendations (Textual)</h3><ul id="21f2881f-86f1-80ff-a7bc-f14e7a99e91f" class="bulleted-list"><li style="list-style-type:disc"><strong>Top section:</strong> Clearly labeled input fields grouped logically (boiler, vent properties)</li></ul><ul id="21f2881f-86f1-803f-8743-c967db56e20e" class="bulleted-list"><li style="list-style-type:disc"><strong>Middle section:</strong> Live validation messages displayed next to corresponding inputs</li></ul><ul id="21f2881f-86f1-803a-8165-c2c474b0bb25" class="bulleted-list"><li style="list-style-type:disc"><strong>Bottom section:</strong> Real-time calculation results panel, graphical “Draft vs. Height” SVG quick chart</li></ul><hr id="21f2881f-86f1-80cf-8ee4-ec2b2b7d0917"/><h2 id="21f2881f-86f1-8019-a1b0-f3b13482fb5f" class="">5. Integration Plan</h2><h3 id="21f2881f-86f1-80a4-8a15-cc119e25ec2f" class="">Ecosystem Integration</h3><ul id="21f2881f-86f1-80c4-8854-c12f3465f508" class="bulleted-list"><li style="list-style-type:disc">Shared validators and unit converters across SizeWise tools</li></ul><ul id="21f2881f-86f1-803c-b3a4-cc4b31606df2" class="bulleted-list"><li style="list-style-type:disc">Modular structure with toolConfig.json for seamless plug-and-play integration</li></ul><h3 id="21f2881f-86f1-80fa-b4ac-d1b476211c76" class="">Data Management</h3><ul id="21f2881f-86f1-803b-86fd-d9b3ed6ae792" class="bulleted-list"><li style="list-style-type:disc">Immediate export of results (PDF/CSV)</li></ul><ul id="21f2881f-86f1-80f2-885d-e890b8a837a9" class="bulleted-list"><li style="list-style-type:disc">Offline caching using browser storage (localStorage)</li></ul><ul id="21f2881f-86f1-807d-8165-fc7f424963f5" class="bulleted-list"><li style="list-style-type:disc">Future persistence (SQLite/Postgres, Phase 3.2)</li></ul><hr id="21f2881f-86f1-8065-984e-c61e9cc09ee0"/><h2 id="21f2881f-86f1-80d5-979e-e120fde16834" class="">6. Validation &amp; Standards Compliance</h2><h3 id="21f2881f-86f1-8067-bcb8-c01d7c73049b" class="">Validation Approach</h3><ul id="21f2881f-86f1-8099-b4d6-d847f82446c4" class="bulleted-list"><li style="list-style-type:disc">Strict inline validation to prevent invalid user inputs</li></ul><ul id="21f2881f-86f1-8044-8210-dce3cab5d5c2" class="bulleted-list"><li style="list-style-type:disc">Post-calculation warnings for borderline draft pressures, velocities</li></ul><h3 id="21f2881f-86f1-801a-9996-dc6eec745f8d" class="">NFPA &amp; IFGC Compliance Logic</h3><ul id="21f2881f-86f1-8058-8e11-d113f5a81869" class="bulleted-list"><li style="list-style-type:disc">NFPA 54 as primary validation standard</li></ul><ul id="21f2881f-86f1-80a2-83d2-d90abb1cd202" class="bulleted-list"><li style="list-style-type:disc">Secondary IFGC warnings clearly indicated but not enforced</li></ul><ul id="21f2881f-86f1-800a-8e2d-e82c20f26f66" class="bulleted-list"><li style="list-style-type:disc">Centralized standards_validation module references NFPA rules from JSON/YAML constants</li></ul><hr id="21f2881f-86f1-8005-a6e7-c89d355be7aa"/><h2 id="21f2881f-86f1-8079-b3bd-ff384830c3bb" class="">7. Implementation Roadmap</h2><h3 id="21f2881f-86f1-802f-9476-ef695501e134" class="">Task-Level Breakdown</h3><ul id="21f2881f-86f1-80b2-8bc3-d3cb9baec913" class="bulleted-list"><li style="list-style-type:disc"><strong>Sprint 1:</strong> Setup folders, define schemas, constants, and initial formulas</li></ul><ul id="21f2881f-86f1-80ef-a623-ffb50d88f025" class="bulleted-list"><li style="list-style-type:disc"><strong>Sprint 2:</strong> Implement core calculation logic, initial UI, API endpoints</li></ul><ul id="21f2881f-86f1-80ed-bb35-fb638c7d59f1" class="bulleted-list"><li style="list-style-type:disc"><strong>Sprint 3:</strong> Integrate inline validation, NFPA compliance logic</li></ul><ul id="21f2881f-86f1-80d0-9cb5-ce86cd66b401" class="bulleted-list"><li style="list-style-type:disc"><strong>Sprint 4:</strong> Add PDF/CSV export functionality, SVG graph, complete unit and integration tests</li></ul><h3 id="21f2881f-86f1-80b0-96ca-ef0b0ffa36e4" class="">Timeline and Dependencies</h3><ul id="21f2881f-86f1-80c8-93cc-ff35f18a9746" class="bulleted-list"><li style="list-style-type:disc">Total timeline: 4 sprints (2 weeks per sprint)</li></ul><ul id="21f2881f-86f1-80ac-a9fd-ce5c20f9b0f1" class="bulleted-list"><li style="list-style-type:disc">Begins immediately after Grease Duct Sizer MVP completion</li></ul><ul id="21f2881f-86f1-8091-a739-f4e912223b32" class="bulleted-list"><li style="list-style-type:disc">Dependencies: Existing SizeWise tool framework, completion of Grease Duct Sizer</li></ul><hr id="21f2881f-86f1-8013-bc28-f4ab66436a46"/><h2 id="21f2881f-86f1-8036-be5f-fccfdbb0e453" class="">8. Documentation Plan</h2><h3 id="21f2881f-86f1-80ae-bb98-d41558618f22" class="">Internal Code Documentation</h3><ul id="21f2881f-86f1-80dd-88bb-db0eb08d804c" class="bulleted-list"><li style="list-style-type:disc">Comprehensive inline documentation for functions and modules</li></ul><ul id="21f2881f-86f1-8004-8e50-cb82b54177c8" class="bulleted-list"><li style="list-style-type:disc">Auto-generated API documentation (Sphinx or MkDocs)</li></ul><h3 id="21f2881f-86f1-80cb-9505-dde954114c70" class="">User Documentation</h3><ul id="21f2881f-86f1-80e7-aa9b-d823e3502dc8" class="bulleted-list"><li style="list-style-type:disc">User-facing help page clearly describing inputs, outputs, NFPA compliance logic</li></ul><ul id="21f2881f-86f1-8081-af4a-f21faadb8a6d" class="bulleted-list"><li style="list-style-type:disc">Example usage scenarios provided</li></ul><hr id="21f2881f-86f1-80d6-8952-e946044229b1"/><h2 id="21f2881f-86f1-80e0-bdab-c9714b6f2838" class="">9. Testing Plan</h2><h3 id="21f2881f-86f1-80ee-a9fd-dd31c72a6bc1" class="">Unit, Integration, E2E Testing</h3><ul id="21f2881f-86f1-8084-a750-e9fd1f475057" class="bulleted-list"><li style="list-style-type:disc">Unit tests: Jest (for React components and TypeScript logic)</li></ul><ul id="21f2881f-86f1-80c1-ae1d-c2c87ec62219" class="bulleted-list"><li style="list-style-type:disc">Integration/E2E tests: Playwright for UI validation</li></ul><h3 id="21f2881f-86f1-8018-ab93-f1f7977db595" class="">Automation &amp; CI/CD</h3><ul id="21f2881f-86f1-807a-8cc7-e4de5cb7e7b3" class="bulleted-list"><li style="list-style-type:disc">Automated testing configured via GitHub Actions</li></ul><ul id="21f2881f-86f1-80f0-b319-d8340d9c0625" class="bulleted-list"><li style="list-style-type:disc">Pipeline includes build verification, unit/integration test execution, and deployment automation</li></ul><hr id="21f2881f-86f1-8044-8512-ee568c9615c9"/><h2 id="21f2881f-86f1-8012-9e7e-e5f8df8de102" class="">10. Risk &amp; Mitigation Plan</h2><h3 id="21f2881f-86f1-803e-abb6-cbbed12dbee2" class="">Potential Risks</h3><ol type="1" id="21f2881f-86f1-80ae-8b04-fc763b443751" class="numbered-list" start="1"><li><strong>NFPA standard changes</strong><ul id="21f2881f-86f1-8055-a68e-c16aff67cf52" class="bulleted-list"><li style="list-style-type:disc">Mitigation: Modular rules file easily updated without affecting core logic</li></ul></li></ol><ol type="1" id="21f2881f-86f1-8020-b418-e8667930b5ca" class="numbered-list" start="2"><li><strong>Frontend/Backend Integration Delays</strong><ul id="21f2881f-86f1-804e-9b94-f80e5c66bc16" class="bulleted-list"><li style="list-style-type:disc">Mitigation: Clearly defined API schema; interim mock API responses</li></ul></li></ol><ol type="1" id="21f2881f-86f1-8064-b527-d49967918cc9" class="numbered-list" start="3"><li><strong>Validation Logic Complexity</strong><ul id="21f2881f-86f1-80aa-a929-c3232ab6f9bb" class="bulleted-list"><li style="list-style-type:disc">Mitigation: Structured and modular validation modules, detailed unit tests</li></ul></li></ol><ol type="1" id="21f2881f-86f1-80e2-8f71-d849e459229c" class="numbered-list" start="4"><li><strong>Offline Mode Limitations</strong><ul id="21f2881f-86f1-80b6-bfd0-ce014acbf113" class="bulleted-list"><li style="list-style-type:disc">Mitigation: Thorough offline capability testing with clearly documented limitations</li></ul></li></ol><ol type="1" id="21f2881f-86f1-80f1-842d-f2c52acfac26" class="numbered-list" start="5"><li><strong>Timeline Slippage</strong><ul id="21f2881f-86f1-80e6-815e-ee12a4f5f734" class="bulleted-list"><li style="list-style-type:disc">Mitigation: Regular sprint reviews, clear milestone tracking, buffer for unexpected delays</li></ul></li></ol><hr id="21f2881f-86f1-8067-a07c-d29f4aad473f"/><p id="21f2881f-86f1-8005-af02-d50fa44a72a6" class="">This comprehensive planning document provides a clear implementation strategy ensuring efficient, accurate delivery of the Boiler Vent Sizer tool within your SizeWise Suite.</p><p id="21f2881f-86f1-8037-b383-f37dd146cee4" class="">
</p><table id="21f2881f-86f1-80bd-a8f1-e35a525adfde" class="simple-table"><tbody><tr id="21f2881f-86f1-80d5-8042-cfa4d76d05cb"><td id="JF_H" class="">#</td><td id="MaBy" class="">Section</td><td id=";a=g" class=""><strong>Updated / New Sub-Sections &amp; Bullets</strong></td></tr><tr id="21f2881f-86f1-802a-872d-f724d3b58e59"><td id="JF_H" class=""><strong>2</strong></td><td id="MaBy" class=""><strong>Functional Requirements</strong></td><td id=";a=g" class=""><strong>2.2 Core Inputs</strong> • <code>scaleFactor</code> (px → unit) (required) • <code>units</code> enum (<code>imperial</code> default, <code>metric</code>) • <code>node.type</code> pick-list inc. <code>tee_lateral</code>, <code>elbow_angle</code>, <code>tee_angle</code>, <code>tee_boot_angle</code>, <code>termination.type</code><strong>2.4 Calculation Logic Summary</strong> • Hybrid engine: <strong>Table-first, Physics-fallback</strong> decision tree<strong>2.5 Validation Rules</strong> • JSON soft-schema checks; unknown keys ⇒ warnings, not fatal • Auto-insert <code>drain_cap</code> (½ in NPT) on first horiz→vert condensing leg • Segment/Node cap: <strong>100 max each</strong>; hard error if exceeded</td></tr><tr id="21f2881f-86f1-80e9-afca-e310bdc8e40d"><td id="JF_H" class=""><strong>3</strong></td><td id="MaBy" class=""><strong>Technical Architecture</strong></td><td id=";a=g" class=""><strong>3.2 Component &amp; Module Diagram</strong> • <code>VentCanvas.tsx</code> (React-Konva) • <code>draftWorker.ts</code> (Web Worker wrapper) • <code>networkParser.ts</code> (graph builder) • <code>tableEngine.ts</code> (NFPA look-ups) • <code>physicsEngine.ts</code> (Darcy-Weisbach + temp loop) • <code>validator.ts</code> (category flip, condensation check)<strong>3.4 Folder / Package Structure</strong><code>text\nboiler_venting/\n ├─ canvas/ # drawing &amp; drag-drop UI\n ├─ solver/ # parser · tableEngine · physicsEngine · draftWorker\n ├─ validation/ # nfpaRules.json · validator.ts\n ├─ services/ # exportPdf.ts · exportCsv.ts\n ├─ hooks/ # useVentNetwork()\n ├─ types/ # ventNetwork.d.ts (auto-generated)\n └─ …\n</code><strong>3.6 API Design &amp; Schemas</strong> • <strong>Canvas ↔ Solver Data Contract</strong> <code>ventNetwork.schema.json</code> (v 1.0.0) • Required keys: <code>schemaVersion</code>, <code>units</code>, <code>scaleFactor</code>, <code>nodes[]</code>, <code>segments[]</code> • Soft validation via AJV; returns {errors[], warnings[]} object • TS types generated via <code>json-schema-to-ts</code></td></tr><tr id="21f2881f-86f1-80e1-ba6e-d04ce4d0a82b"><td id="JF_H" class=""><strong>4</strong></td><td id="MaBy" class=""><strong>Integration Plan</strong></td><td id=";a=g" class=""><strong>4.1 Interaction with Existing Tools</strong> • Shares <code>useUnitSystem()</code> hook with DuctSizer &amp; GreaseDuctSizer<strong>4.2 Shared Services</strong> • Common <code>exportPdf</code> &amp; <code>exportCsv</code> utilities now aware of vent network JSON</td></tr><tr id="21f2881f-86f1-8042-b773-d937d24e6e94"><td id="JF_H" class=""><strong>5</strong></td><td id="MaBy" class=""><strong>UI / UX Specifications</strong></td><td id=";a=g" class=""><strong>5.4 Screen &amp; Component Catalogue</strong> • “Initial Conditions” modal → boiler data, insulation, environment • Canvas palette adds icons for new node types (lateral tee, angle tee…)<strong>5.6 Real-time Feedback &amp; States</strong> • Segment colour = green/amber/red based on <code>validator</code> result • Debounce 200 ms; Web Worker keeps main thread 60 fps</td></tr><tr id="21f2881f-86f1-804c-9535-e8c38defb460"><td id="JF_H" class=""><strong>6</strong></td><td id="MaBy" class=""><strong>Validation &amp; Standards Compliance</strong></td><td id=";a=g" class=""><strong>6.2 Rule-Engine Architecture</strong> • Tier 1 = NFPA table match • Tier 2 = Physics calc with NFPA safety factor<strong>6.3 Compliance Data Tables</strong> • <code>nfpaRules.json</code> (capacity, clearance, material) version-tagged<strong>6.4 Update &amp; Version Control</strong> • Schema version (<code>1.0.0</code>) + Rule-set version (NFPA 54-2021) stored with every project</td></tr><tr id="21f2881f-86f1-80d8-8baf-df9c698b136f"><td id="JF_H" class=""><strong>11</strong></td><td id="MaBy" class=""><strong>Implementation Roadmap</strong></td><td id=";a=g" class=""><strong>11.1 Phase &amp; Sprint Breakdown</strong> (excerpt)• Sprint 0 – JSON schema + TS types + test fixtures• Sprint 1 – Canvas palette &amp; node editor• Sprint 2 – Hybrid solver (table + physics) in Web Worker• Sprint 3 – Validator &amp; colour-coding, PDF/CSV export<strong>11.2 Milestones</strong> M1 Schema 🔒 • M2 Interactive canvas ✅ • M3 Solver 100-segment pass ✅ • M4 NFPA validation pass/fail ✅</td></tr><tr id="21f2881f-86f1-80c6-8aaf-ef1a127d16f3"><td id="JF_H" class=""><strong>16</strong></td><td id="MaBy" class=""><strong>Appendices</strong></td><td id=";a=g" class=""><strong>16.2 Sample Input/Output</strong><code>json\n{\n \&quot;schemaVersion\&quot;: \&quot;1.0.0\&quot;,\n \&quot;units\&quot;: \&quot;imperial\&quot;,\n \&quot;scaleFactor\&quot;: 25,\n \&quot;nodes\&quot;: [ … ],\n \&quot;segments\&quot;: [ … ]\n}\n</code><strong>16.4 Reference Tables &amp; Charts</strong> → added link to NFPA 54 Table 13-1 capacity excerpt</td></tr></tbody></table><h2 id="21f2881f-86f1-8007-986e-df056fe1d139" class="">HLN – Section 2 Functional Requirements</h2><blockquote id="21f2881f-86f1-8083-870b-de77e9b12311" class="">This narrative expands each 2.x sub-section to show what the Boiler Vent Sizer must do and why it matters. Every bullet is implementation-ready and traceable to user value or code compliance.</blockquote><hr id="21f2881f-86f1-80f4-8eae-eebcc2cd20df"/><h3 id="21f2881f-86f1-809a-8e1b-daf415de9dcd" class="">2.1 User Stories &amp; Primary Use-Cases</h3><table id="21f2881f-86f1-80f5-b117-fd9976bd0948" class="simple-table"><tbody><tr id="21f2881f-86f1-80fe-b173-ef7d6b347879"><td id="@h[F" class="">ID</td><td id="gX\[" class="">As a …</td><td id="qMND" class="">I want to …</td><td id="kSsK" class="">So that …</td></tr><tr id="21f2881f-86f1-80a1-befc-ec9ec8b5eb51"><td id="@h[F" class="">US-01</td><td id="gX\[" class="">HVAC engineer</td><td id="qMND" class="">draw a complete flue layout (≤ 100 segments) on a canvas</td><td id="kSsK" class="">I can visualise and size vents during design</td></tr><tr id="21f2881f-86f1-80fd-8a9e-c1bacecc5a3a"><td id="@h[F" class="">US-02</td><td id="gX\[" class="">Estimator</td><td id="qMND" class="">override diameters on any segment</td><td id="kSsK" class="">I can quote alternate materials/diameters for VE options</td></tr><tr id="21f2881f-86f1-8079-8f15-d1356f505f8e"><td id="@h[F" class="">US-03</td><td id="gX\[" class="">Engineer</td><td id="qMND" class="">see real-time draft, ΔP, velocity, and NFPA category per segment</td><td id="kSsK" class="">I can spot code violations instantly</td></tr><tr id="21f2881f-86f1-80a2-851d-efab2fd1636f"><td id="@h[F" class="">US-04</td><td id="gX\[" class="">Inspector</td><td id="qMND" class="">export a PDF/CSV calc sheet</td><td id="kSsK" class="">I can file proof-of-compliance with permit docs</td></tr><tr id="21f2881f-86f1-8092-b388-f31057af82b8"><td id="@h[F" class="">US-05</td><td id="gX\[" class="">Sales rep</td><td id="qMND" class="">toggle between Imperial / Metric units</td><td id="kSsK" class="">I can support US and international bids without re-entry</td></tr><tr id="21f2881f-86f1-8016-83a9-e00011d4a785"><td id="@h[F" class="">US-06</td><td id="gX\[" class="">Engineer</td><td id="qMND" class="">receive warnings when a segment condenses</td><td id="kSsK" class="">I can change material or add drains before corrosion occurs</td></tr></tbody></table><hr id="21f2881f-86f1-80a6-a966-ea4c94c0c010"/><h3 id="21f2881f-86f1-8048-9f96-c333bc5cde92" class="">2.2 Core Inputs <em>(validated via JSON schema v 1.0.0)</em></h3><table id="21f2881f-86f1-80a6-8ed8-ebb80e2629c9" class="simple-table"><tbody><tr id="21f2881f-86f1-8071-8c08-de25b26ebf51"><td id="bgoq" class="">Group</td><td id="Liap" class="">Field</td><td id="wy=d" class="">Notes / Allowed Values</td></tr><tr id="21f2881f-86f1-804e-bd3b-c6b9ee1ef3cc"><td id="bgoq" class=""><strong>Global</strong></td><td id="Liap" class=""><code>units</code></td><td id="wy=d" class=""><code>imperial</code> (°F, in, ft) - <strong>default</strong></td></tr><tr id="21f2881f-86f1-8096-a19f-d180914d9d82"><td id="bgoq" class=""></td><td id="Liap" class=""><code>scaleFactor</code></td><td id="wy=d" class="">pixels → length (e.g. 25 px = 1 ft); canvas can zoom but solver always works in real units</td></tr><tr id="21f2881f-86f1-80ff-abf1-c28cceb00b42"><td id="bgoq" class=""><strong>Nodes</strong></td><td id="Liap" class=""><code>type</code></td><td id="wy=d" class=""><code>boiler</code>, <code>termination</code>, <code>elbow_90</code>, <code>elbow_angle</code>, <code>tee_straight</code>, <code>tee_lateral</code>, <code>tee_boot</code>, <code>tee_angle</code>, <code>tee_boot_angle</code>, <code>drain_cap</code>, <code>cleanout_cap</code></td></tr><tr id="21f2881f-86f1-80e2-992b-e616c2d09a8d"><td id="bgoq" class=""></td><td id="Liap" class=""><code>position</code></td><td id="wy=d" class=""><code>[x_px, y_px]</code> – UI only</td></tr><tr id="21f2881f-86f1-8071-afa5-da43f3a9d902"><td id="bgoq" class=""></td><td id="Liap" class=""><code>termination.type</code></td><td id="wy=d" class=""><code>open</code>, <code>rain_cap</code>, <code>flue_cone</code>, <code>sidewall_hood</code></td></tr><tr id="21f2881f-86f1-80fa-b48c-de9165bca851"><td id="bgoq" class=""><strong>Segments</strong></td><td id="Liap" class=""><code>length</code></td><td id="wy=d" class="">real-world units, positive non-zero</td></tr><tr id="21f2881f-86f1-8069-b7f6-d2f90bdd097b"><td id="bgoq" class=""></td><td id="Liap" class=""><code>diameter</code></td><td id="wy=d" class="">auto-suggested → user-editable</td></tr><tr id="21f2881f-86f1-802d-b5d0-e181042a621c"><td id="bgoq" class=""></td><td id="Liap" class=""><code>material</code></td><td id="wy=d" class="">enum (<code>TypeB</code>, <code>304SS</code>, …)</td></tr><tr id="21f2881f-86f1-80c2-9e41-c6a5e9d382e5"><td id="bgoq" class=""></td><td id="Liap" class=""><code>insulation.thickness</code></td><td id="wy=d" class="">required if insulation ≠ None</td></tr><tr id="21f2881f-86f1-801d-968d-ed7b3f9fec98"><td id="bgoq" class=""></td><td id="Liap" class=""><code>insulation.material</code></td><td id="wy=d" class="">pick-list (<code>Fiberglass</code>, <code>MineralWool</code>, <code>Aerogel</code>, <code>Other</code>)</td></tr><tr id="21f2881f-86f1-809d-8b6c-d7e9f84af693"><td id="bgoq" class=""></td><td id="Liap" class=""><code>pitch</code></td><td id="wy=d" class="">° or in/ft; UI enforces ≥ 0.25 in/ft rise toward termination (NFPA 54 §5.3.10)</td></tr><tr id="21f2881f-86f1-80ed-be17-d5f4bd8027f7"><td id="bgoq" class=""><strong>Boiler</strong></td><td id="Liap" class=""><code>capacity</code></td><td id="wy=d" class="">MBH / kW</td></tr><tr id="21f2881f-86f1-808c-907d-d88ceb7c5d6a"><td id="bgoq" class=""></td><td id="Liap" class=""><code>turnDown</code></td><td id="wy=d" class="">ratio (e.g. 4:1) optional</td></tr><tr id="21f2881f-86f1-801d-ab5b-dd7a78092807"><td id="bgoq" class=""></td><td id="Liap" class=""><code>flueTempHigh</code>, <code>flueTempLow</code></td><td id="wy=d" class="">°F / °C</td></tr></tbody></table><hr id="21f2881f-86f1-809b-8c5f-c54ad0a12206"/><h3 id="21f2881f-86f1-8062-9d05-dc33dbcd56b3" class="">2.3 Core Outputs</h3><table id="21f2881f-86f1-803c-b7a4-d2267d053751" class="simple-table"><tbody><tr id="21f2881f-86f1-80b6-924b-d3bad1da0292"><td id="_]Ee" class="">Level</td><td id="RIhd" class="">Output</td><td id="DtLS" class="">Meaning / Use</td></tr><tr id="21f2881f-86f1-80a9-a4ed-f3a861adab9e"><td id="_]Ee" class=""><strong>Per-segment</strong></td><td id="RIhd" class="">Diameter (suggested vs. user) • Velocity • ΔP_friction • ΔP_minor • Draft available • Flue-gas T_out • NFPA Cat (I–IV) • Condensing flag • Pass/Fail colour</td><td id="DtLS" class=""></td></tr><tr id="21f2881f-86f1-8036-9012-e74d4df36220"><td id="_]Ee" class=""><strong>Whole network</strong></td><td id="RIhd" class="">Total ΔP (boiler → termination) • Overall draft margin • Combustion-air requirement (cfm) • Cap count (drain, clean-out) • Code summary table</td><td id="DtLS" class=""></td></tr><tr id="21f2881f-86f1-8025-b3d6-dc6e427f7a06"><td id="_]Ee" class=""><strong>Exports</strong></td><td id="RIhd" class="">PDF (detailed table + schematic) • CSV (row = segment)</td><td id="DtLS" class=""></td></tr></tbody></table><hr id="21f2881f-86f1-8088-bb3c-c30aef8e864b"/><h3 id="21f2881f-86f1-8098-a8dc-c183ea77c84a" class="">2.4 Calculation Logic Summary</h3><ol type="1" id="21f2881f-86f1-806c-bd90-c97ef71e5559" class="numbered-list" start="1"><li><strong>Continuity</strong>: <code>ṁ</code> constant along tree; branch split uses connected load (US-03).</li></ol><ol type="1" id="21f2881f-86f1-803b-b00f-d2fc2f594557" class="numbered-list" start="2"><li><strong>Hybrid Solver Path</strong><ol type="a" id="21f2881f-86f1-80a3-a97a-c080d7de551f" class="numbered-list" start="1"><li><strong>Table Engine</strong> – queries NFPA 54 Tbl 13-1 for Type B natural-draft segments; returns capacity OK/NG.</li></ol><ol type="a" id="21f2881f-86f1-80ca-8c2b-e3aaccff7da2" class="numbered-list" start="2"><li><strong>Physics Engine</strong> – Darcy-Weisbach + ΣK + buoyancy (natural draft) → iterates until |ΔP_residual| &lt; 0.1 Pa.</li></ol><ol type="a" id="21f2881f-86f1-800b-b7b0-c3a9a910ee2d" class="numbered-list" start="3"><li><strong>Merge Rule</strong> – if both apply, use the stricter diameter / largest ΔP.</li></ol></li></ol><ol type="1" id="21f2881f-86f1-80c4-9931-c7cdede3ba48" class="numbered-list" start="3"><li><strong>Heat-loss Loop</strong> – updates ρ &amp; draft via <code>T_drop = Q_loss/(ṁ·c_p)</code>; triggers condensation check.</li></ol><ol type="1" id="21f2881f-86f1-80ac-94a9-ec42a73df028" class="numbered-list" start="4"><li><strong>Auto-drain Logic</strong> – first horizontal→vertical condensing leg inserts <code>drain_cap</code> (½ in NPT).</li></ol><ol type="1" id="21f2881f-86f1-80d8-ae62-fa85f71d180e" class="numbered-list" start="5"><li><strong>Propagation</strong> – diameter edits cascade upstream (boiler → termination) unless user locks a child segment.</li></ol><hr id="21f2881f-86f1-80ca-8cf6-f8c8b5f54f7c"/><h3 id="21f2881f-86f1-80a8-987b-faeb11145acc" class="">2.5 Validation Rules <em>(soft schema + code rules)</em></h3><table id="21f2881f-86f1-8029-9361-f5579dbf08b9" class="simple-table"><tbody><tr id="21f2881f-86f1-80bd-a700-ea4989a30488"><td id="dZoq" class="">Category</td><td id="lm&lt;^" class="">Rule</td></tr><tr id="21f2881f-86f1-8031-bd40-c563d2137899"><td id="dZoq" class=""><strong>Schema</strong></td><td id="lm&lt;^" class="">Unknown keys → warning; missing required → block save</td></tr><tr id="21f2881f-86f1-80a6-8746-cd633e89e42f"><td id="dZoq" class=""><strong>Structural</strong></td><td id="lm&lt;^" class="">≤ 100 nodes AND ≤ 100 segments</td></tr><tr id="21f2881f-86f1-806c-8f61-dcff0eda2662"><td id="dZoq" class=""><strong>Geometry</strong></td><td id="lm&lt;^" class="">No negative length; no self-loop; pitch ≥ 0.25 in/ft</td></tr><tr id="21f2881f-86f1-8033-8d65-c4a42fc0f264"><td id="dZoq" class=""><strong>Code Compliance</strong></td><td id="lm&lt;^" class="">Draft at appliance ≥ –0.01 in w.c. (NFPA 54 Annex G) • Clearance &amp; material per Cat</td></tr><tr id="21f2881f-86f1-80e0-9260-eadf8db741a7"><td id="dZoq" class=""><strong>Condensation Flip</strong></td><td id="lm&lt;^" class="">If <code>T_wall</code> &lt; dew-point → recalc as Cat II/IV</td></tr><tr id="21f2881f-86f1-80da-b19b-d2eb92595804"><td id="dZoq" class=""><strong>Capacity</strong></td><td id="lm&lt;^" class="">Table capacity overrun ⇒ red segment</td></tr><tr id="21f2881f-86f1-80df-97ef-da52041f3cb8"><td id="dZoq" class=""><strong>Units</strong></td><td id="lm&lt;^" class="">Mixed units rejected; all real-value fields inherit <code>units</code> flag</td></tr></tbody></table><hr id="21f2881f-86f1-800c-958d-e96d29a81f68"/><h3 id="21f2881f-86f1-80c7-b14f-f42e2138465a" class="">2.6 Error &amp; Exception Handling</h3><table id="21f2881f-86f1-80d5-93b3-f80a1ce02204" class="simple-table"><tbody><tr id="21f2881f-86f1-8022-883e-d05b80e2e9bb"><td id="OUzn" class="">Level</td><td id="rzFs" class="">Condition</td><td id="QwDt" class="">UX Outcome</td></tr><tr id="21f2881f-86f1-80a6-bfb5-f5c704a5fffa"><td id="OUzn" class=""><strong>Schema</strong></td><td id="rzFs" class="">Invalid JSON or &gt; 100 segments</td><td id="QwDt" class="">Modal error – prevent import</td></tr><tr id="21f2881f-86f1-80df-b300-ebe1835cd916"><td id="OUzn" class=""><strong>Solver</strong></td><td id="rzFs" class="">Non-convergent physics (10 iters)</td><td id="QwDt" class="">Red banner “Cannot converge – check diameters or loops”</td></tr><tr id="21f2881f-86f1-8088-921b-f42cddf062ab"><td id="OUzn" class=""><strong>Validation</strong></td><td id="rzFs" class="">Code violation</td><td id="QwDt" class="">Segment turns <strong>red</strong> + tooltip reason</td></tr><tr id="21f2881f-86f1-80fb-be3c-ead4d2325c2a"><td id="OUzn" class=""><strong>Auto-drain</strong></td><td id="rzFs" class="">Drain node auto-insert but user deletes</td><td id="QwDt" class="">Warning badge “Condensate drain required by NFPA 54”</td></tr></tbody></table><hr id="21f2881f-86f1-800a-b179-fe6cb5d05726"/><h3 id="21f2881f-86f1-80da-9f25-d5562b16efca" class="">2.7 Non-Functional Requirements</h3><table id="21f2881f-86f1-80d5-ae27-fd03cd490fd2" class="simple-table"><tbody><tr id="21f2881f-86f1-805d-a54a-e64ed9b8acfe"><td id="eWAu" class="">Topic</td><td id="Y}Ft" class="">Requirement</td></tr><tr id="21f2881f-86f1-8071-974d-e746c4de0873"><td id="eWAu" class=""><strong>Usability</strong></td><td id="Y}Ft" class="">Drag-and-drop &lt; 20 ms pointer lag • Undo/redo (20 levels)</td></tr><tr id="21f2881f-86f1-80d4-88bb-f358b6a80269"><td id="eWAu" class=""><strong>Performance</strong></td><td id="Y}Ft" class="">Re-solve network ≤ 100 ms for 100-segment tree on mid-2020 laptop</td></tr><tr id="21f2881f-86f1-80fd-b38a-d0802e6735d0"><td id="eWAu" class=""><strong>Audit</strong></td><td id="Y}Ft" class="">Export includes <code>schemaVersion</code>, NFPA version, solver path (table/physics)</td></tr><tr id="21f2881f-86f1-80f9-880f-c820b49db61c"><td id="eWAu" class=""><strong>Localization</strong></td><td id="Y}Ft" class="">Imperial default; switch = “gear” menu → metric; UI re-labels instantly</td></tr><tr id="21f2881f-86f1-8089-90d8-e52bd7af79d3"><td id="eWAu" class=""><strong>Offline</strong></td><td id="Y}Ft" class="">Canvas + solver works fully offline (localStorage)</td></tr><tr id="21f2881f-86f1-8073-b042-f2cb1e8d4376"><td id="eWAu" class=""><strong>Accessibility</strong></td><td id="Y}Ft" class="">Colour/shape indicators; screen-reader alt labels on nodes</td></tr></tbody></table><hr id="21f2881f-86f1-80cb-95d7-d4f37401b291"/><p id="21f2881f-86f1-805b-92f5-ccf8dbcf4726" class=""><strong>Engineering judgement required</strong> to fine-tune constants (e.g., convergence tolerance) during implementation, but this 2.x narrative now captures <em>what</em> must be built and <em>why</em> it meets user and NFPA needs.</p></div></article><span class="sans" style="font-size:14px;padding-top:2em"></span></body></html>