<html><head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"/><title>1.1 About SizeWise Suite</title><style>
/* cspell:disable-file */
/* webkit printing magic: print all background colors */
html {
	-webkit-print-color-adjust: exact;
}
* {
	box-sizing: border-box;
	-webkit-print-color-adjust: exact;
}

html,
body {
	margin: 0;
	padding: 0;
}
@media only screen {
	body {
		margin: 2em auto;
		max-width: 900px;
		color: rgb(55, 53, 47);
	}
}

body {
	line-height: 1.5;
	white-space: pre-wrap;
}

a,
a.visited {
	color: inherit;
	text-decoration: underline;
}

.pdf-relative-link-path {
	font-size: 80%;
	color: #444;
}

h1,
h2,
h3 {
	letter-spacing: -0.01em;
	line-height: 1.2;
	font-weight: 600;
	margin-bottom: 0;
}

.page-title {
	font-size: 2.5rem;
	font-weight: 700;
	margin-top: 0;
	margin-bottom: 0.75em;
}

h1 {
	font-size: 1.875rem;
	margin-top: 1.875rem;
}

h2 {
	font-size: 1.5rem;
	margin-top: 1.5rem;
}

h3 {
	font-size: 1.25rem;
	margin-top: 1.25rem;
}

.source {
	border: 1px solid #ddd;
	border-radius: 3px;
	padding: 1.5em;
	word-break: break-all;
}

.callout {
	border-radius: 3px;
	padding: 1rem;
}

figure {
	margin: 1.25em 0;
	page-break-inside: avoid;
}

figcaption {
	opacity: 0.5;
	font-size: 85%;
	margin-top: 0.5em;
}

mark {
	background-color: transparent;
}

.indented {
	padding-left: 1.5em;
}

hr {
	background: transparent;
	display: block;
	width: 100%;
	height: 1px;
	visibility: visible;
	border: none;
	border-bottom: 1px solid rgba(55, 53, 47, 0.09);
}

img {
	max-width: 100%;
}

@media only print {
	img {
		max-height: 100vh;
		object-fit: contain;
	}
}

@page {
	margin: 1in;
}

.collection-content {
	font-size: 0.875rem;
}

.column-list {
	display: flex;
	justify-content: space-between;
}

.column {
	padding: 0 1em;
}

.column:first-child {
	padding-left: 0;
}

.column:last-child {
	padding-right: 0;
}

.table_of_contents-item {
	display: block;
	font-size: 0.875rem;
	line-height: 1.3;
	padding: 0.125rem;
}

.table_of_contents-indent-1 {
	margin-left: 1.5rem;
}

.table_of_contents-indent-2 {
	margin-left: 3rem;
}

.table_of_contents-indent-3 {
	margin-left: 4.5rem;
}

.table_of_contents-link {
	text-decoration: none;
	opacity: 0.7;
	border-bottom: 1px solid rgba(55, 53, 47, 0.18);
}

table,
th,
td {
	border: 1px solid rgba(55, 53, 47, 0.09);
	border-collapse: collapse;
}

table {
	border-left: none;
	border-right: none;
}

th,
td {
	font-weight: normal;
	padding: 0.25em 0.5em;
	line-height: 1.5;
	min-height: 1.5em;
	text-align: left;
}

th {
	color: rgba(55, 53, 47, 0.6);
}

ol,
ul {
	margin: 0;
	margin-block-start: 0.6em;
	margin-block-end: 0.6em;
}

li > ol:first-child,
li > ul:first-child {
	margin-block-start: 0.6em;
}

ul > li {
	list-style: disc;
}

ul.to-do-list {
	padding-inline-start: 0;
}

ul.to-do-list > li {
	list-style: none;
}

.to-do-children-checked {
	text-decoration: line-through;
	opacity: 0.375;
}

ul.toggle > li {
	list-style: none;
}

ul {
	padding-inline-start: 1.7em;
}

ul > li {
	padding-left: 0.1em;
}

ol {
	padding-inline-start: 1.6em;
}

ol > li {
	padding-left: 0.2em;
}

.mono ol {
	padding-inline-start: 2em;
}

.mono ol > li {
	text-indent: -0.4em;
}

.toggle {
	padding-inline-start: 0em;
	list-style-type: none;
}

/* Indent toggle children */
.toggle > li > details {
	padding-left: 1.7em;
}

.toggle > li > details > summary {
	margin-left: -1.1em;
}

.selected-value {
	display: inline-block;
	padding: 0 0.5em;
	background: rgba(206, 205, 202, 0.5);
	border-radius: 3px;
	margin-right: 0.5em;
	margin-top: 0.3em;
	margin-bottom: 0.3em;
	white-space: nowrap;
}

.collection-title {
	display: inline-block;
	margin-right: 1em;
}

.page-description {
	margin-bottom: 2em;
}

.simple-table {
	margin-top: 1em;
	font-size: 0.875rem;
	empty-cells: show;
}
.simple-table td {
	height: 29px;
	min-width: 120px;
}

.simple-table th {
	height: 29px;
	min-width: 120px;
}

.simple-table-header-color {
	background: rgb(247, 246, 243);
	color: black;
}
.simple-table-header {
	font-weight: 500;
}

time {
	opacity: 0.5;
}

.icon {
	display: inline-block;
	max-width: 1.2em;
	max-height: 1.2em;
	text-decoration: none;
	vertical-align: text-bottom;
	margin-right: 0.5em;
}

img.icon {
	border-radius: 3px;
}

.user-icon {
	width: 1.5em;
	height: 1.5em;
	border-radius: 100%;
	margin-right: 0.5rem;
}

.user-icon-inner {
	font-size: 0.8em;
}

.text-icon {
	border: 1px solid #000;
	text-align: center;
}

.page-cover-image {
	display: block;
	object-fit: cover;
	width: 100%;
	max-height: 30vh;
}

.page-header-icon {
	font-size: 3rem;
	margin-bottom: 1rem;
}

.page-header-icon-with-cover {
	margin-top: -0.72em;
	margin-left: 0.07em;
}

.page-header-icon img {
	border-radius: 3px;
}

.link-to-page {
	margin: 1em 0;
	padding: 0;
	border: none;
	font-weight: 500;
}

p > .user {
	opacity: 0.5;
}

td > .user,
td > time {
	white-space: nowrap;
}

input[type="checkbox"] {
	transform: scale(1.5);
	margin-right: 0.6em;
	vertical-align: middle;
}

p {
	margin-top: 0.5em;
	margin-bottom: 0.5em;
}

.image {
	border: none;
	margin: 1.5em 0;
	padding: 0;
	border-radius: 0;
	text-align: center;
}

.code,
code {
	background: rgba(135, 131, 120, 0.15);
	border-radius: 3px;
	padding: 0.2em 0.4em;
	border-radius: 3px;
	font-size: 85%;
	tab-size: 2;
}

code {
	color: #eb5757;
}

.code {
	padding: 1.5em 1em;
}

.code-wrap {
	white-space: pre-wrap;
	word-break: break-all;
}

.code > code {
	background: none;
	padding: 0;
	font-size: 100%;
	color: inherit;
}

blockquote {
	font-size: 1.25em;
	margin: 1em 0;
	padding-left: 1em;
	border-left: 3px solid rgb(55, 53, 47);
}

.bookmark {
	text-decoration: none;
	max-height: 8em;
	padding: 0;
	display: flex;
	width: 100%;
	align-items: stretch;
}

.bookmark-title {
	font-size: 0.85em;
	overflow: hidden;
	text-overflow: ellipsis;
	height: 1.75em;
	white-space: nowrap;
}

.bookmark-text {
	display: flex;
	flex-direction: column;
}

.bookmark-info {
	flex: 4 1 180px;
	padding: 12px 14px 14px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.bookmark-image {
	width: 33%;
	flex: 1 1 180px;
	display: block;
	position: relative;
	object-fit: cover;
	border-radius: 1px;
}

.bookmark-description {
	color: rgba(55, 53, 47, 0.6);
	font-size: 0.75em;
	overflow: hidden;
	max-height: 4.5em;
	word-break: break-word;
}

.bookmark-href {
	font-size: 0.75em;
	margin-top: 0.25em;
}

.sans { font-family: ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol"; }
.code { font-family: "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace; }
.serif { font-family: Lyon-Text, Georgia, ui-serif, serif; }
.mono { font-family: iawriter-mono, Nitti, Menlo, Courier, monospace; }
.pdf .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK JP'; }
.pdf:lang(zh-CN) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK SC'; }
.pdf:lang(zh-TW) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK TC'; }
.pdf:lang(ko-KR) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK KR'; }
.pdf .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK JP'; }
.pdf:lang(zh-CN) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC'; }
.pdf:lang(zh-TW) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK TC'; }
.pdf:lang(ko-KR) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK KR'; }
.pdf .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK JP'; }
.pdf:lang(zh-CN) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK SC'; }
.pdf:lang(zh-TW) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK TC'; }
.pdf:lang(ko-KR) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK KR'; }
.pdf .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK JP'; }
.pdf:lang(zh-CN) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC'; }
.pdf:lang(zh-TW) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK TC'; }
.pdf:lang(ko-KR) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK KR'; }
.highlight-default {
	color: rgba(50, 48, 44, 1);
}
.highlight-gray {
	color: rgba(115, 114, 110, 1);
	fill: rgba(115, 114, 110, 1);
}
.highlight-brown {
	color: rgba(159, 107, 83, 1);
	fill: rgba(159, 107, 83, 1);
}
.highlight-orange {
	color: rgba(217, 115, 13, 1);
	fill: rgba(217, 115, 13, 1);
}
.highlight-yellow {
	color: rgba(203, 145, 47, 1);
	fill: rgba(203, 145, 47, 1);
}
.highlight-teal {
	color: rgba(68, 131, 97, 1);
	fill: rgba(68, 131, 97, 1);
}
.highlight-blue {
	color: rgba(51, 126, 169, 1);
	fill: rgba(51, 126, 169, 1);
}
.highlight-purple {
	color: rgba(144, 101, 176, 1);
	fill: rgba(144, 101, 176, 1);
}
.highlight-pink {
	color: rgba(193, 76, 138, 1);
	fill: rgba(193, 76, 138, 1);
}
.highlight-red {
	color: rgba(205, 60, 58, 1);
	fill: rgba(205, 60, 58, 1);
}
.highlight-default_background {
	color: rgba(50, 48, 44, 1);
}
.highlight-gray_background {
	background: rgba(248, 248, 247, 1);
}
.highlight-brown_background {
	background: rgba(244, 238, 238, 1);
}
.highlight-orange_background {
	background: rgba(251, 236, 221, 1);
}
.highlight-yellow_background {
	background: rgba(251, 243, 219, 1);
}
.highlight-teal_background {
	background: rgba(237, 243, 236, 1);
}
.highlight-blue_background {
	background: rgba(231, 243, 248, 1);
}
.highlight-purple_background {
	background: rgba(248, 243, 252, 1);
}
.highlight-pink_background {
	background: rgba(252, 241, 246, 1);
}
.highlight-red_background {
	background: rgba(253, 235, 236, 1);
}
.block-color-default {
	color: inherit;
	fill: inherit;
}
.block-color-gray {
	color: rgba(115, 114, 110, 1);
	fill: rgba(115, 114, 110, 1);
}
.block-color-brown {
	color: rgba(159, 107, 83, 1);
	fill: rgba(159, 107, 83, 1);
}
.block-color-orange {
	color: rgba(217, 115, 13, 1);
	fill: rgba(217, 115, 13, 1);
}
.block-color-yellow {
	color: rgba(203, 145, 47, 1);
	fill: rgba(203, 145, 47, 1);
}
.block-color-teal {
	color: rgba(68, 131, 97, 1);
	fill: rgba(68, 131, 97, 1);
}
.block-color-blue {
	color: rgba(51, 126, 169, 1);
	fill: rgba(51, 126, 169, 1);
}
.block-color-purple {
	color: rgba(144, 101, 176, 1);
	fill: rgba(144, 101, 176, 1);
}
.block-color-pink {
	color: rgba(193, 76, 138, 1);
	fill: rgba(193, 76, 138, 1);
}
.block-color-red {
	color: rgba(205, 60, 58, 1);
	fill: rgba(205, 60, 58, 1);
}
.block-color-default_background {
	color: inherit;
	fill: inherit;
}
.block-color-gray_background {
	background: rgba(248, 248, 247, 1);
}
.block-color-brown_background {
	background: rgba(244, 238, 238, 1);
}
.block-color-orange_background {
	background: rgba(251, 236, 221, 1);
}
.block-color-yellow_background {
	background: rgba(251, 243, 219, 1);
}
.block-color-teal_background {
	background: rgba(237, 243, 236, 1);
}
.block-color-blue_background {
	background: rgba(231, 243, 248, 1);
}
.block-color-purple_background {
	background: rgba(248, 243, 252, 1);
}
.block-color-pink_background {
	background: rgba(252, 241, 246, 1);
}
.block-color-red_background {
	background: rgba(253, 235, 236, 1);
}
.select-value-color-default { background-color: rgba(84, 72, 49, 0.08); }
.select-value-color-gray { background-color: rgba(84, 72, 49, 0.15); }
.select-value-color-brown { background-color: rgba(210, 162, 141, 0.35); }
.select-value-color-orange { background-color: rgba(224, 124, 57, 0.27); }
.select-value-color-yellow { background-color: rgba(236, 191, 66, 0.39); }
.select-value-color-green { background-color: rgba(123, 183, 129, 0.27); }
.select-value-color-blue { background-color: rgba(93, 165, 206, 0.27); }
.select-value-color-purple { background-color: rgba(168, 129, 197, 0.27); }
.select-value-color-pink { background-color: rgba(225, 136, 179, 0.27); }
.select-value-color-red { background-color: rgba(244, 171, 159, 0.4); }

.checkbox {
	display: inline-flex;
	vertical-align: text-bottom;
	width: 16;
	height: 16;
	background-size: 16px;
	margin-left: 2px;
	margin-right: 5px;
}

.checkbox-on {
	background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20width%3D%2216%22%20height%3D%2216%22%20fill%3D%22%2358A9D7%22%2F%3E%0A%3Cpath%20d%3D%22M6.71429%2012.2852L14%204.9995L12.7143%203.71436L6.71429%209.71378L3.28571%206.2831L2%207.57092L6.71429%2012.2852Z%22%20fill%3D%22white%22%2F%3E%0A%3C%2Fsvg%3E");
}

.checkbox-off {
	background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20x%3D%220.75%22%20y%3D%220.75%22%20width%3D%2214.5%22%20height%3D%2214.5%22%20fill%3D%22white%22%20stroke%3D%22%2336352F%22%20stroke-width%3D%221.5%22%2F%3E%0A%3C%2Fsvg%3E");
}
	
</style></head><body><article id="2262881f-86f1-8074-9565-ef8e60de4e51" class="page sans"><header><h1 class="page-title">1.1 About SizeWise Suite</h1><p class="page-description"></p></header><div class="page-body"><p id="2262881f-86f1-8005-aad5-feabe8a6e016" class=""><strong>About SizeWise Suite</strong></p><p id="2262881f-86f1-8000-947e-d18e1126f7d2" class="">
</p><p id="2262881f-86f1-80c9-98af-db7b2ebbe1ad" class=""><strong>SizeWise Suite</strong> is a modular, offline-capable HVAC engineering and estimating platform designed to unify duct sizing, vent design, and cost estimating in a single, standards-driven workspace. Specifically crafted for mechanical engineers who require precise standards validation, estimators seeking enhanced accuracy and speed, QA professionals aiming for compliance efficiency, and project managers needing real-time oversight and strategic insight, SizeWise ensures rapid, repeatable, and code-compliant workflows—whether users are operating from an office or remote job sites with unreliable internet access.</p><h2 id="2262881f-86f1-805e-a0a4-dea95ca0d170" class="">Enhanced User Persona Details</h2><ul id="2262881f-86f1-8081-ad1b-d5edca1c3b99" class="bulleted-list"><li style="list-style-type:disc"><strong>Mechanical Engineers</strong>: Efficiently validate duct and vent designs against industry standards such as SMACNA and NFPA, substantially reducing errors and rework.</li></ul><ul id="2262881f-86f1-8093-a3f1-cf6d79233b1d" class="bulleted-list"><li style="list-style-type:disc"><strong>Estimators</strong>: Conduct precise quantity takeoffs and cost estimations faster, enhancing bid accuracy and competitiveness.</li></ul><ul id="2262881f-86f1-8026-b779-e027d10feaaa" class="bulleted-list"><li style="list-style-type:disc"><strong>QA Professionals</strong>: Utilize built-in validation features to swiftly identify non-compliant inputs and ensure strict adherence to standards.</li></ul><ul id="2262881f-86f1-8033-a124-e0b11d73420c" class="bulleted-list"><li style="list-style-type:disc"><strong>Project Managers</strong>: Access real-time project insights regarding costs, progress, and compliance, enhancing oversight and strategic decision-making.</li></ul><h2 id="2262881f-86f1-80b9-9179-ea4daa25bdd2" class="">Technical Framework Details</h2><p id="2262881f-86f1-80c5-8d28-cb34a42c073d" class="">SizeWise Suite utilizes a centralized, schema-driven architecture with AJV/Zod for comprehensive JSON schema validation, which helps prevent errors and ensures consistent data handling across modules, ensuring data integrity throughout all operations. Its integrated bidirectional IP/SI unit-conversion engine provides reliable, seamless conversions, dynamically supporting both imperial and metric standards.</p><h2 id="2262881f-86f1-8043-80e4-e0b08bfc224f" class="">Core Benefits</h2><table id="2262881f-86f1-802a-9bda-eea89579b8f8" class="simple-table"><thead class="simple-table-header"><tr id="2262881f-86f1-80d6-bdb1-c867bcde60ea"><th id="kG&gt;X" class="simple-table-header-color simple-table-header">Feature</th><th id="{LFQ" class="simple-table-header-color simple-table-header">Benefit to Users</th><th id="Rgat" class="simple-table-header-color simple-table-header">Practical Example &amp; Quantifiable Metrics</th></tr></thead><tbody><tr id="2262881f-86f1-8030-9922-fc18f55b2bf0"><td id="kG&gt;X" class=""><strong>Unified HVAC Toolchain</strong></td><td id="{LFQ" class="">One platform for duct sizing, vent design, and estimating</td><td id="Rgat" class="">Reduces project initiation time by up to 65% through automated data entry and integrated workflow management; seamless module integration</td></tr><tr id="2262881f-86f1-8065-a356-e2afd90d553c"><td id="kG&gt;X" class=""><strong>Standards-Aligned Logic</strong></td><td id="{LFQ" class="">Integrated dynamic validation per SMACNA, NFPA, and ASHRAE</td><td id="Rgat" class="">Achieves 99% compliance with HVAC standards; provides real-time input warnings</td></tr><tr id="2262881f-86f1-807d-a7db-ca04b633feb0"><td id="kG&gt;X" class=""><strong>Offline-First Design</strong></td><td id="{LFQ" class="">Reliable operation in remote or disconnected environments</td><td id="Rgat" class="">Eliminates up to 40% downtime caused by unreliable connectivity</td></tr><tr id="2262881f-86f1-80a3-9fc8-ce8317a90499"><td id="kG&gt;X" class=""><strong>Structured Export System</strong></td><td id="{LFQ" class="">Automated PDF, Excel, CSV, and BIM-compatible exports</td><td id="Rgat" class="">Cuts documentation preparation time by up to 50%; rapid generation of accurate bids and reports</td></tr><tr id="2262881f-86f1-8053-bffe-dd1106c984a5"><td id="kG&gt;X" class=""><strong>Plugin-Ready Architecture</strong></td><td id="{LFQ" class="">Highly scalable with minimal integration overhead</td><td id="Rgat" class="">Reduces development time for new modules by 70%; easily expandable to include additional modules</td></tr></tbody></table><h2 id="2262881f-86f1-80be-aaa5-ed0a678053e0" class="">Core Modules (Phase 0.0)</h2><ul id="2262881f-86f1-8009-8a89-c21241a1bfc1" class="bulleted-list"><li style="list-style-type:disc"><strong>Air Duct Sizer</strong><p id="2262881f-86f1-8020-ae6f-cdb77109c831" class="">Friction-loss sizing per SMACNA standards, including velocity and gauge validation, with seamless integration into estimating workflows.</p></li></ul><ul id="2262881f-86f1-8062-8f9a-dff801db29e3" class="bulleted-list"><li style="list-style-type:disc"><strong>Grease Duct Sizer</strong><p id="2262881f-86f1-80ce-9fb3-e21f30562044" class="">Comprehensive NFPA 96 compliance, hood airflow optimization, and clearance management, directly interoperable with the Air Duct and Estimating modules.</p></li></ul><ul id="2262881f-86f1-80e3-b1d7-dcec183ce278" class="bulleted-list"><li style="list-style-type:disc"><strong>Engine Exhaust Sizer</strong><p id="2262881f-86f1-8028-980e-e1f981bc4999" class="">High-velocity exhaust design specifically for generators and Combined Heat and Power (CHP) systems, ensuring accurate plume dynamics and module integration.</p></li></ul><ul id="2262881f-86f1-8030-8bb5-dfd1d945c546" class="bulleted-list"><li style="list-style-type:disc"><strong>Boiler Vent Sizer</strong><p id="2262881f-86f1-8011-b1ff-e4a898edc359" class="">Detailed sizing for Category I–IV appliance vents, incorporating draft pressures, temperature management, and elevation adjustments, integrated directly into cost estimation processes, automatically populating relevant data fields and reducing manual entry, thus minimizing errors and significantly improving workflow efficiency.</p></li></ul><ul id="2262881f-86f1-8091-9f61-db4eb9ea3ac9" class="bulleted-list"><li style="list-style-type:disc"><strong>Estimating App</strong> <em>(NEW)</em><p id="2262881f-86f1-807a-b1ed-d3f5da8b33b2" class="">A comprehensive estimating solution addressing labor/material takeoffs, management of alternates and exclusions, and automated formatted bid exports. Directly integrated with engineering modules to enhance accuracy and workflow efficiency. 📦 <em>(Phase 0.0 – Estimating Core Setup)</em></p></li></ul><h2 id="2262881f-86f1-802c-b3f3-c7f32f692b47" class="">Core Value Proposition</h2><p id="2262881f-86f1-80bd-82f7-fa6671efb551" class="">SizeWise Suite addresses the fragmented nature of traditional HVAC workflows by integrating sizing, venting, and estimating processes within a single intuitive environment. In response to increasing HVAC project complexity and evolving regulatory standards, SizeWise Suite offers substantial cost and time savings through:</p><ul id="2262881f-86f1-803d-9149-ecba77935cdc" class="bulleted-list"><li style="list-style-type:disc">Reduced manual data entry and associated errors</li></ul><ul id="2262881f-86f1-8074-9d8d-df0d77e68988" class="bulleted-list"><li style="list-style-type:disc">Consistent adherence to up-to-date industry standards</li></ul><ul id="2262881f-86f1-80ea-8e9a-dcb995e83e0d" class="bulleted-list"><li style="list-style-type:disc">Streamlined documentation with automated, structured outputs</li></ul><ul id="2262881f-86f1-807d-aad3-c2b325216b54" class="bulleted-list"><li style="list-style-type:disc">Sustained productivity, regardless of network availability</li></ul><h2 id="2262881f-86f1-80b9-8bc1-d347845bf3f8" class="">Customer Testimonials (Placeholder)</h2><p id="2262881f-86f1-806f-9a3b-d101c0a0f3f8" class=""><em>Real-world customer success stories demonstrating measurable operational efficiencies, compliance enhancements, and quantifiable cost savings achieved through SizeWise Suite will be collected and added on a quarterly basis, starting Q4 2025, following completion of project milestones and successful client implementations.</em></p><p id="2262881f-86f1-80b8-8471-f3d50d961fd8" class="">
</p><h2 id="2262881f-86f1-8083-93f9-ec8b3a11670f" class="">Key Takeaways</h2><ul id="2262881f-86f1-80e8-86fe-ce882c1da86f" class="bulleted-list"><li style="list-style-type:disc"><strong>Unified Platform:</strong> SizeWise Suite integrates duct sizing, vent design, and cost estimating in a single, standards-driven workspace.</li></ul><ul id="2262881f-86f1-80bb-82c9-f4ca6442e0e0" class="bulleted-list"><li style="list-style-type:disc"><strong>Offline Capability:</strong> Ensures productivity in remote locations with unreliable internet, reducing downtime by up to 40%.</li></ul><ul id="2262881f-86f1-80d2-b200-eeaddef7d1a2" class="bulleted-list"><li style="list-style-type:disc"><strong>Standards Compliance:</strong> Built-in validation against SMACNA, NFPA, and ASHRAE standards, achieving 99% compliance.</li></ul><ul id="2262881f-86f1-8030-b342-fb42b3f66d01" class="bulleted-list"><li style="list-style-type:disc"><strong>Efficiency Gains:</strong> Reduces project initiation time by up to 65% and documentation preparation time by 50%.</li></ul><ul id="2262881f-86f1-80df-adc3-f9929f5d6885" class="bulleted-list"><li style="list-style-type:disc"><strong>Modular Design:</strong> Core modules include Air Duct Sizer, Grease Duct Sizer, Engine Exhaust Sizer, Boiler Vent Sizer, and a new Estimating App.</li></ul><ul id="2262881f-86f1-80eb-9d09-e7efadf98ae0" class="bulleted-list"><li style="list-style-type:disc"><strong>Target Users:</strong> Designed for mechanical engineers, estimators, QA professionals, and project managers.</li></ul><p id="2262881f-86f1-8074-9d53-d78cdc314257" class="">
</p></div></article><span class="sans" style="font-size:14px;padding-top:2em"></span></body></html>