<html><head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"/><title>1.4 Document Structure</title><style>
/* cspell:disable-file */
/* webkit printing magic: print all background colors */
html {
	-webkit-print-color-adjust: exact;
}
* {
	box-sizing: border-box;
	-webkit-print-color-adjust: exact;
}

html,
body {
	margin: 0;
	padding: 0;
}
@media only screen {
	body {
		margin: 2em auto;
		max-width: 900px;
		color: rgb(55, 53, 47);
	}
}

body {
	line-height: 1.5;
	white-space: pre-wrap;
}

a,
a.visited {
	color: inherit;
	text-decoration: underline;
}

.pdf-relative-link-path {
	font-size: 80%;
	color: #444;
}

h1,
h2,
h3 {
	letter-spacing: -0.01em;
	line-height: 1.2;
	font-weight: 600;
	margin-bottom: 0;
}

.page-title {
	font-size: 2.5rem;
	font-weight: 700;
	margin-top: 0;
	margin-bottom: 0.75em;
}

h1 {
	font-size: 1.875rem;
	margin-top: 1.875rem;
}

h2 {
	font-size: 1.5rem;
	margin-top: 1.5rem;
}

h3 {
	font-size: 1.25rem;
	margin-top: 1.25rem;
}

.source {
	border: 1px solid #ddd;
	border-radius: 3px;
	padding: 1.5em;
	word-break: break-all;
}

.callout {
	border-radius: 3px;
	padding: 1rem;
}

figure {
	margin: 1.25em 0;
	page-break-inside: avoid;
}

figcaption {
	opacity: 0.5;
	font-size: 85%;
	margin-top: 0.5em;
}

mark {
	background-color: transparent;
}

.indented {
	padding-left: 1.5em;
}

hr {
	background: transparent;
	display: block;
	width: 100%;
	height: 1px;
	visibility: visible;
	border: none;
	border-bottom: 1px solid rgba(55, 53, 47, 0.09);
}

img {
	max-width: 100%;
}

@media only print {
	img {
		max-height: 100vh;
		object-fit: contain;
	}
}

@page {
	margin: 1in;
}

.collection-content {
	font-size: 0.875rem;
}

.column-list {
	display: flex;
	justify-content: space-between;
}

.column {
	padding: 0 1em;
}

.column:first-child {
	padding-left: 0;
}

.column:last-child {
	padding-right: 0;
}

.table_of_contents-item {
	display: block;
	font-size: 0.875rem;
	line-height: 1.3;
	padding: 0.125rem;
}

.table_of_contents-indent-1 {
	margin-left: 1.5rem;
}

.table_of_contents-indent-2 {
	margin-left: 3rem;
}

.table_of_contents-indent-3 {
	margin-left: 4.5rem;
}

.table_of_contents-link {
	text-decoration: none;
	opacity: 0.7;
	border-bottom: 1px solid rgba(55, 53, 47, 0.18);
}

table,
th,
td {
	border: 1px solid rgba(55, 53, 47, 0.09);
	border-collapse: collapse;
}

table {
	border-left: none;
	border-right: none;
}

th,
td {
	font-weight: normal;
	padding: 0.25em 0.5em;
	line-height: 1.5;
	min-height: 1.5em;
	text-align: left;
}

th {
	color: rgba(55, 53, 47, 0.6);
}

ol,
ul {
	margin: 0;
	margin-block-start: 0.6em;
	margin-block-end: 0.6em;
}

li > ol:first-child,
li > ul:first-child {
	margin-block-start: 0.6em;
}

ul > li {
	list-style: disc;
}

ul.to-do-list {
	padding-inline-start: 0;
}

ul.to-do-list > li {
	list-style: none;
}

.to-do-children-checked {
	text-decoration: line-through;
	opacity: 0.375;
}

ul.toggle > li {
	list-style: none;
}

ul {
	padding-inline-start: 1.7em;
}

ul > li {
	padding-left: 0.1em;
}

ol {
	padding-inline-start: 1.6em;
}

ol > li {
	padding-left: 0.2em;
}

.mono ol {
	padding-inline-start: 2em;
}

.mono ol > li {
	text-indent: -0.4em;
}

.toggle {
	padding-inline-start: 0em;
	list-style-type: none;
}

/* Indent toggle children */
.toggle > li > details {
	padding-left: 1.7em;
}

.toggle > li > details > summary {
	margin-left: -1.1em;
}

.selected-value {
	display: inline-block;
	padding: 0 0.5em;
	background: rgba(206, 205, 202, 0.5);
	border-radius: 3px;
	margin-right: 0.5em;
	margin-top: 0.3em;
	margin-bottom: 0.3em;
	white-space: nowrap;
}

.collection-title {
	display: inline-block;
	margin-right: 1em;
}

.page-description {
	margin-bottom: 2em;
}

.simple-table {
	margin-top: 1em;
	font-size: 0.875rem;
	empty-cells: show;
}
.simple-table td {
	height: 29px;
	min-width: 120px;
}

.simple-table th {
	height: 29px;
	min-width: 120px;
}

.simple-table-header-color {
	background: rgb(247, 246, 243);
	color: black;
}
.simple-table-header {
	font-weight: 500;
}

time {
	opacity: 0.5;
}

.icon {
	display: inline-block;
	max-width: 1.2em;
	max-height: 1.2em;
	text-decoration: none;
	vertical-align: text-bottom;
	margin-right: 0.5em;
}

img.icon {
	border-radius: 3px;
}

.user-icon {
	width: 1.5em;
	height: 1.5em;
	border-radius: 100%;
	margin-right: 0.5rem;
}

.user-icon-inner {
	font-size: 0.8em;
}

.text-icon {
	border: 1px solid #000;
	text-align: center;
}

.page-cover-image {
	display: block;
	object-fit: cover;
	width: 100%;
	max-height: 30vh;
}

.page-header-icon {
	font-size: 3rem;
	margin-bottom: 1rem;
}

.page-header-icon-with-cover {
	margin-top: -0.72em;
	margin-left: 0.07em;
}

.page-header-icon img {
	border-radius: 3px;
}

.link-to-page {
	margin: 1em 0;
	padding: 0;
	border: none;
	font-weight: 500;
}

p > .user {
	opacity: 0.5;
}

td > .user,
td > time {
	white-space: nowrap;
}

input[type="checkbox"] {
	transform: scale(1.5);
	margin-right: 0.6em;
	vertical-align: middle;
}

p {
	margin-top: 0.5em;
	margin-bottom: 0.5em;
}

.image {
	border: none;
	margin: 1.5em 0;
	padding: 0;
	border-radius: 0;
	text-align: center;
}

.code,
code {
	background: rgba(135, 131, 120, 0.15);
	border-radius: 3px;
	padding: 0.2em 0.4em;
	border-radius: 3px;
	font-size: 85%;
	tab-size: 2;
}

code {
	color: #eb5757;
}

.code {
	padding: 1.5em 1em;
}

.code-wrap {
	white-space: pre-wrap;
	word-break: break-all;
}

.code > code {
	background: none;
	padding: 0;
	font-size: 100%;
	color: inherit;
}

blockquote {
	font-size: 1.25em;
	margin: 1em 0;
	padding-left: 1em;
	border-left: 3px solid rgb(55, 53, 47);
}

.bookmark {
	text-decoration: none;
	max-height: 8em;
	padding: 0;
	display: flex;
	width: 100%;
	align-items: stretch;
}

.bookmark-title {
	font-size: 0.85em;
	overflow: hidden;
	text-overflow: ellipsis;
	height: 1.75em;
	white-space: nowrap;
}

.bookmark-text {
	display: flex;
	flex-direction: column;
}

.bookmark-info {
	flex: 4 1 180px;
	padding: 12px 14px 14px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.bookmark-image {
	width: 33%;
	flex: 1 1 180px;
	display: block;
	position: relative;
	object-fit: cover;
	border-radius: 1px;
}

.bookmark-description {
	color: rgba(55, 53, 47, 0.6);
	font-size: 0.75em;
	overflow: hidden;
	max-height: 4.5em;
	word-break: break-word;
}

.bookmark-href {
	font-size: 0.75em;
	margin-top: 0.25em;
}

.sans { font-family: ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol"; }
.code { font-family: "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace; }
.serif { font-family: Lyon-Text, Georgia, ui-serif, serif; }
.mono { font-family: iawriter-mono, Nitti, Menlo, Courier, monospace; }
.pdf .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK JP'; }
.pdf:lang(zh-CN) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK SC'; }
.pdf:lang(zh-TW) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK TC'; }
.pdf:lang(ko-KR) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK KR'; }
.pdf .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK JP'; }
.pdf:lang(zh-CN) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC'; }
.pdf:lang(zh-TW) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK TC'; }
.pdf:lang(ko-KR) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK KR'; }
.pdf .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK JP'; }
.pdf:lang(zh-CN) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK SC'; }
.pdf:lang(zh-TW) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK TC'; }
.pdf:lang(ko-KR) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK KR'; }
.pdf .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK JP'; }
.pdf:lang(zh-CN) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC'; }
.pdf:lang(zh-TW) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK TC'; }
.pdf:lang(ko-KR) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK KR'; }
.highlight-default {
	color: rgba(50, 48, 44, 1);
}
.highlight-gray {
	color: rgba(115, 114, 110, 1);
	fill: rgba(115, 114, 110, 1);
}
.highlight-brown {
	color: rgba(159, 107, 83, 1);
	fill: rgba(159, 107, 83, 1);
}
.highlight-orange {
	color: rgba(217, 115, 13, 1);
	fill: rgba(217, 115, 13, 1);
}
.highlight-yellow {
	color: rgba(203, 145, 47, 1);
	fill: rgba(203, 145, 47, 1);
}
.highlight-teal {
	color: rgba(68, 131, 97, 1);
	fill: rgba(68, 131, 97, 1);
}
.highlight-blue {
	color: rgba(51, 126, 169, 1);
	fill: rgba(51, 126, 169, 1);
}
.highlight-purple {
	color: rgba(144, 101, 176, 1);
	fill: rgba(144, 101, 176, 1);
}
.highlight-pink {
	color: rgba(193, 76, 138, 1);
	fill: rgba(193, 76, 138, 1);
}
.highlight-red {
	color: rgba(205, 60, 58, 1);
	fill: rgba(205, 60, 58, 1);
}
.highlight-default_background {
	color: rgba(50, 48, 44, 1);
}
.highlight-gray_background {
	background: rgba(248, 248, 247, 1);
}
.highlight-brown_background {
	background: rgba(244, 238, 238, 1);
}
.highlight-orange_background {
	background: rgba(251, 236, 221, 1);
}
.highlight-yellow_background {
	background: rgba(251, 243, 219, 1);
}
.highlight-teal_background {
	background: rgba(237, 243, 236, 1);
}
.highlight-blue_background {
	background: rgba(231, 243, 248, 1);
}
.highlight-purple_background {
	background: rgba(248, 243, 252, 1);
}
.highlight-pink_background {
	background: rgba(252, 241, 246, 1);
}
.highlight-red_background {
	background: rgba(253, 235, 236, 1);
}
.block-color-default {
	color: inherit;
	fill: inherit;
}
.block-color-gray {
	color: rgba(115, 114, 110, 1);
	fill: rgba(115, 114, 110, 1);
}
.block-color-brown {
	color: rgba(159, 107, 83, 1);
	fill: rgba(159, 107, 83, 1);
}
.block-color-orange {
	color: rgba(217, 115, 13, 1);
	fill: rgba(217, 115, 13, 1);
}
.block-color-yellow {
	color: rgba(203, 145, 47, 1);
	fill: rgba(203, 145, 47, 1);
}
.block-color-teal {
	color: rgba(68, 131, 97, 1);
	fill: rgba(68, 131, 97, 1);
}
.block-color-blue {
	color: rgba(51, 126, 169, 1);
	fill: rgba(51, 126, 169, 1);
}
.block-color-purple {
	color: rgba(144, 101, 176, 1);
	fill: rgba(144, 101, 176, 1);
}
.block-color-pink {
	color: rgba(193, 76, 138, 1);
	fill: rgba(193, 76, 138, 1);
}
.block-color-red {
	color: rgba(205, 60, 58, 1);
	fill: rgba(205, 60, 58, 1);
}
.block-color-default_background {
	color: inherit;
	fill: inherit;
}
.block-color-gray_background {
	background: rgba(248, 248, 247, 1);
}
.block-color-brown_background {
	background: rgba(244, 238, 238, 1);
}
.block-color-orange_background {
	background: rgba(251, 236, 221, 1);
}
.block-color-yellow_background {
	background: rgba(251, 243, 219, 1);
}
.block-color-teal_background {
	background: rgba(237, 243, 236, 1);
}
.block-color-blue_background {
	background: rgba(231, 243, 248, 1);
}
.block-color-purple_background {
	background: rgba(248, 243, 252, 1);
}
.block-color-pink_background {
	background: rgba(252, 241, 246, 1);
}
.block-color-red_background {
	background: rgba(253, 235, 236, 1);
}
.select-value-color-default { background-color: rgba(84, 72, 49, 0.08); }
.select-value-color-gray { background-color: rgba(84, 72, 49, 0.15); }
.select-value-color-brown { background-color: rgba(210, 162, 141, 0.35); }
.select-value-color-orange { background-color: rgba(224, 124, 57, 0.27); }
.select-value-color-yellow { background-color: rgba(236, 191, 66, 0.39); }
.select-value-color-green { background-color: rgba(123, 183, 129, 0.27); }
.select-value-color-blue { background-color: rgba(93, 165, 206, 0.27); }
.select-value-color-purple { background-color: rgba(168, 129, 197, 0.27); }
.select-value-color-pink { background-color: rgba(225, 136, 179, 0.27); }
.select-value-color-red { background-color: rgba(244, 171, 159, 0.4); }

.checkbox {
	display: inline-flex;
	vertical-align: text-bottom;
	width: 16;
	height: 16;
	background-size: 16px;
	margin-left: 2px;
	margin-right: 5px;
}

.checkbox-on {
	background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20width%3D%2216%22%20height%3D%2216%22%20fill%3D%22%2358A9D7%22%2F%3E%0A%3Cpath%20d%3D%22M6.71429%2012.2852L14%204.9995L12.7143%203.71436L6.71429%209.71378L3.28571%206.2831L2%207.57092L6.71429%2012.2852Z%22%20fill%3D%22white%22%2F%3E%0A%3C%2Fsvg%3E");
}

.checkbox-off {
	background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20x%3D%220.75%22%20y%3D%220.75%22%20width%3D%2214.5%22%20height%3D%2214.5%22%20fill%3D%22white%22%20stroke%3D%22%2336352F%22%20stroke-width%3D%221.5%22%2F%3E%0A%3C%2Fsvg%3E");
}
	
</style></head><body><article id="2262881f-86f1-80e8-9bf6-f05cf5606167" class="page sans"><header><h1 class="page-title">1.4 Document Structure</h1><p class="page-description"></p></header><div class="page-body"><h2 id="2262881f-86f1-8079-86dd-ede9dd7168ae" class="">Document Structure</h2><p id="2262881f-86f1-80e8-95c9-c08c5288fd90" class="">This section presents a structured overview of the SizeWise Suite documentation, tailored for technical users such as engineering majors, QA analysts, and software product developers. It is designed to help users navigate the documentation logically and to access content relevant to system architecture, estimation logic, compliance enforcement, and tool behavior. The document mirrors the development, validation, and implementation lifecycle of the SizeWise Suite and emphasizes clarity, modularity, and traceability.</p><p id="2262881f-86f1-80cd-a2d2-ef061353fc50" class="">Each major section of this document corresponds to specific phases of the product&#x27;s lifecycle or team responsibilities. Whether the user is managing deployments, developing new modules, validating regulatory compliance, or writing support guides, the structure ensures that all reference materials are logically grouped and consistently presented.</p><p id="2262881f-86f1-8077-9ad1-fe41e899a488" class="">Where applicable, hyperlinks and internal bookmarks are embedded throughout this document to improve cross-referencing across related content (e.g., connecting estimation workflows to export logic or validation coverage to compliance rules). As more modules are developed, this document will evolve while maintaining strict versioning via the release notes and changelog. Section 8.6 outlines the documentation change control workflow.</p><h3 id="2262881f-86f1-80e3-8999-dc8e801525dd" class="">SizeWise Home and Interaction</h3><p id="2262881f-86f1-80a1-b537-d4a23edbf861" class="">Introduces foundational knowledge for users new to the platform. These entries establish context, define key terminology, and orient users to how the document is structured and why it exists.</p><h3 id="2262881f-86f1-80db-b890-e4d0b968613f" class=""><strong>Introduction</strong></h3><ul id="2262881f-86f1-8048-a957-cf0dd827c120" class="bulleted-list"><li style="list-style-type:disc">1.1 About SizeWise Suite</li></ul><ul id="2262881f-86f1-8080-bbc9-db98122d7ab2" class="bulleted-list"><li style="list-style-type:disc">1.2 Purpose of This Document</li></ul><ul id="2262881f-86f1-8007-82ff-fe05db0159d0" class="bulleted-list"><li style="list-style-type:disc">1.3 Scope and Audience</li></ul><ul id="2262881f-86f1-800a-8fb6-efff404f3f52" class="bulleted-list"><li style="list-style-type:disc">1.4 Document Structure</li></ul><ul id="2262881f-86f1-8098-a349-fd617ae00264" class="bulleted-list"><li style="list-style-type:disc">1.5 Conventions, Terminology &amp; Units</li></ul><h3 id="2262881f-86f1-8094-b891-e138d968f926" class="">Overview of SizeWise Suite</h3><p id="2262881f-86f1-809a-b2db-caf4423db172" class="">Covers core platform design, capabilities, supported standards, and the future direction of the suite’s roadmap.</p><ul id="2262881f-86f1-808f-a702-e99e23e7239d" class="bulleted-list"><li style="list-style-type:disc"><strong>2.1 Product Architecture &amp; Deployment Options</strong></li></ul><ul id="2262881f-86f1-8013-a5c1-cb22e9d13016" class="bulleted-list"><li style="list-style-type:disc"><strong>2.2 Key Features &amp; Benefits</strong></li></ul><ul id="2262881f-86f1-80ed-af08-e1378471cfcc" class="bulleted-list"><li style="list-style-type:disc"><strong>2.3 Supported Standards &amp; Codes</strong></li></ul><ul id="2262881f-86f1-806d-9d41-e449d37ab93a" class="bulleted-list"><li style="list-style-type:disc"><strong>2.4 Roadmap &amp; Planned Modules</strong></li></ul><h3 id="2262881f-86f1-80c9-8ea6-dddd12f35f94" class="">Getting Started</h3><p id="2262881f-86f1-80d6-8b49-d76abfebe033" class="">Outlines requirements and initial setup for system installation and user provisioning.</p><ul id="2262881f-86f1-802c-93ee-fece6a7ad606" class="bulleted-list"><li style="list-style-type:disc"><strong>3.1 System Requirements (Hardware &amp; Software)</strong></li></ul><ul id="2262881f-86f1-80a5-b638-c5e4a8987822" class="bulleted-list"><li style="list-style-type:disc"><strong>3.2 Installation Procedures (On-Premise / Cloud-Hosted)</strong></li></ul><ul id="2262881f-86f1-80b0-a69c-d3e1ba074209" class="bulleted-list"><li style="list-style-type:disc"><strong>3.3 Licensing Model &amp; Activation</strong></li></ul><ul id="2262881f-86f1-8056-be62-df6784b68e57" class="bulleted-list"><li style="list-style-type:disc"><strong>3.4 Initial Configuration Wizard</strong></li></ul><ul id="2262881f-86f1-803e-9a3e-c4a63ecdead9" class="bulleted-list"><li style="list-style-type:disc"><strong>3.5 User Roles &amp; Permission Matrix</strong></li></ul><h3 id="2262881f-86f1-80a7-b6ad-e44417a5eeab" class="">Planning &amp; Project Setup</h3><p id="2262881f-86f1-8088-979a-fbbb3c737fb1" class="">Describes project creation workflows, data integration from design tools, and scope definition with standards.</p><ul id="2262881f-86f1-80a9-a078-c6bd3281c12f" class="bulleted-list"><li style="list-style-type:disc"><strong>4.1 Creating a New Project</strong></li></ul><ul id="2262881f-86f1-8083-9f0a-d4b641b6a099" class="bulleted-list"><li style="list-style-type:disc"><strong>4.2 Project Templates &amp; Standards Library</strong></li></ul><ul id="2262881f-86f1-806d-adf6-d72d2be31558" class="bulleted-list"><li style="list-style-type:disc"><strong>4.3 Importing External Data (CAD, BIM, Spreadsheets)</strong></li></ul><ul id="2262881f-86f1-800a-b475-e794d74a0175" class="bulleted-list"><li style="list-style-type:disc"><strong>4.4 Defining Design Criteria &amp; Code Packages</strong></li></ul><ul id="2262881f-86f1-8043-a679-e2138a4efb0c" class="bulleted-list"><li style="list-style-type:disc"><strong>4.5 Resource Allocation &amp; Schedule Integration</strong></li></ul><h3 id="2262881f-86f1-8083-bd32-c70a9999ab56" class="">Financial Management &amp; Cost Estimating</h3><p id="2262881f-86f1-803a-8f77-ce0161a0c853" class="">Explains data structuring for pricing and estimation, including integration with financial platforms.</p><ul id="2262881f-86f1-80b5-b091-c489a8789fe3" class="bulleted-list"><li style="list-style-type:disc"><strong>5.1 Cost Database Structure &amp; Currency Support</strong></li></ul><ul id="2262881f-86f1-8053-a87d-c6acf0ed8aa5" class="bulleted-list"><li style="list-style-type:disc"><strong>5.2 Unit Costs, Labor Rates &amp; Markups</strong></li></ul><ul id="2262881f-86f1-8037-bd1b-db62e0637190" class="bulleted-list"><li style="list-style-type:disc"><strong>5.3 Estimation Workflows per Module</strong></li></ul><ul id="2262881f-86f1-806a-8517-c81f62511f05" class="bulleted-list"><li style="list-style-type:disc"><strong>5.4 Budget Tracking, Change Orders &amp; Variance Reports</strong></li></ul><ul id="2262881f-86f1-8081-8647-dcd4bc5e743b" class="bulleted-list"><li style="list-style-type:disc"><strong>5.5 Exporting Estimates to ERP/Accounting Systems</strong></li></ul><h3 id="2262881f-86f1-809f-855b-daa4cb0143ab" class="">Module Reference</h3><p id="2262881f-86f1-80a4-9a56-fc2d1591e4bd" class="">Each tool within the SizeWise Suite—such as duct sizing or combustion venting—is covered in detail with technical specifications and usage patterns.</p><ul id="2262881f-86f1-80ce-9605-fcad69ea1d13" class="bulleted-list"><li style="list-style-type:disc"><strong>6.1 Air Duct Sizer</strong><ul id="2262881f-86f1-8048-bc74-ec2a1625bb87" class="bulleted-list"><li style="list-style-type:circle">6.1.1 Overview &amp; Applications</li></ul><ul id="2262881f-86f1-807a-8513-d669ffbcde33" class="bulleted-list"><li style="list-style-type:circle">6.1.2 Input Parameters</li></ul><ul id="2262881f-86f1-80af-9462-f041ae52956b" class="bulleted-list"><li style="list-style-type:circle">6.1.3 Sizing Algorithms &amp; Friction Methods</li></ul><ul id="2262881f-86f1-8022-a543-da693a5fa2b7" class="bulleted-list"><li style="list-style-type:circle">6.1.4 Outputs: Duct Schedules &amp; Reports</li></ul><ul id="2262881f-86f1-803f-9997-e48375a31ab0" class="bulleted-list"><li style="list-style-type:circle">6.1.5 Worked Example &amp; Best Practices</li></ul></li></ul><ul id="2262881f-86f1-80db-952c-e4f4f2a666c5" class="bulleted-list"><li style="list-style-type:disc"><strong>6.2 Boiler Vent Sizer</strong><ul id="2262881f-86f1-8009-84bb-f58c2eec4afb" class="bulleted-list"><li style="list-style-type:circle">6.2.1 Overview &amp; Applications</li></ul><ul id="2262881f-86f1-80ce-a1d0-f44682d38e1b" class="bulleted-list"><li style="list-style-type:circle">6.2.2 Fuel Types &amp; Combustion Data</li></ul><ul id="2262881f-86f1-80f7-a5b2-d04c49d5d362" class="bulleted-list"><li style="list-style-type:circle">6.2.3 Stack Height &amp; Diameter Calculations</li></ul><ul id="2262881f-86f1-809f-9f11-f0467241f640" class="bulleted-list"><li style="list-style-type:circle">6.2.4 Draft &amp; Temperature Considerations</li></ul><ul id="2262881f-86f1-8001-b1ea-cfd77254a68e" class="bulleted-list"><li style="list-style-type:circle">6.2.5 Sample Calculation &amp; Verification</li></ul></li></ul><ul id="2262881f-86f1-809d-9411-c5e8877a0c36" class="bulleted-list"><li style="list-style-type:disc"><strong>6.3 Grease Duct Sizer</strong><ul id="2262881f-86f1-809a-afca-cdead8da8e45" class="bulleted-list"><li style="list-style-type:circle">6.3.1 Overview &amp; NFPA Compliance</li></ul><ul id="2262881f-86f1-80c4-ae2d-dd90208cba30" class="bulleted-list"><li style="list-style-type:circle">6.3.2 Hood Performance &amp; Capture Velocity</li></ul><ul id="2262881f-86f1-806c-96ca-c5949ff70e67" class="bulleted-list"><li style="list-style-type:circle">6.3.3 Duct Sizing Tables &amp; Code References</li></ul><ul id="2262881f-86f1-803c-ae4f-ddfae4796527" class="bulleted-list"><li style="list-style-type:circle">6.3.4 Fire Enclosure Options &amp; Clearances</li></ul><ul id="2262881f-86f1-803d-8b4d-c84b2191e49d" class="bulleted-list"><li style="list-style-type:circle">6.3.5 Illustrative Example</li></ul></li></ul><ul id="2262881f-86f1-80ab-8f1a-f0e578de65fa" class="bulleted-list"><li style="list-style-type:disc"><strong>6.4 Engine Exhaust Sizer</strong><ul id="2262881f-86f1-8061-ba48-eb8ca385e4c7" class="bulleted-list"><li style="list-style-type:circle">6.4.1 Overview &amp; Engine Types</li></ul><ul id="2262881f-86f1-8066-a1bb-d8ea4121c8ae" class="bulleted-list"><li style="list-style-type:circle">6.4.2 Emissions Data &amp; Plume Rise</li></ul><ul id="2262881f-86f1-800f-8568-d400f313fb9b" class="bulleted-list"><li style="list-style-type:circle">6.4.3 Duct &amp; Stack Sizing for Hot Gases</li></ul><ul id="2262881f-86f1-8093-b9e5-cb2c2e970297" class="bulleted-list"><li style="list-style-type:circle">6.4.4 Noise Attenuation &amp; Silencer Selection</li></ul><ul id="2262881f-86f1-804c-b714-dc822b384075" class="bulleted-list"><li style="list-style-type:circle">6.4.5 Worked Example</li></ul></li></ul><ul id="2262881f-86f1-8023-9814-ee6f93cee34c" class="bulleted-list"><li style="list-style-type:disc"><strong>6.5 Future Modules</strong> (See Section 2.4 Roadmap for development sequence)<ul id="2262881f-86f1-801f-bf4b-f71ba5546341" class="bulleted-list"><li style="list-style-type:circle">6.5.1 Anticipated Functionality</li></ul><ul id="2262881f-86f1-8047-ba36-fe59f4fc52d1" class="bulleted-list"><li style="list-style-type:circle">6.5.2 Integration Points &amp; Data Flows</li></ul></li></ul><h3 id="2262881f-86f1-80ff-8b89-fc196ed1fcfb" class="">User Interface &amp; Workflows</h3><p id="2262881f-86f1-8096-8f75-e1a1b96d7597" class="">Outlines user-facing interactions, workflow automation, validation logic, and UI consistency across the suite. Accessibility guidance for WCAG conformance is included.</p><ul id="2262881f-86f1-80e4-8458-eed7560ff52a" class="bulleted-list"><li style="list-style-type:disc"><strong>7.1 Dashboard Overview &amp; Navigation</strong></li></ul><ul id="2262881f-86f1-8041-a231-f8564b34d1ba" class="bulleted-list"><li style="list-style-type:disc"><strong>7.2 Data Entry Forms &amp; Field Validation</strong></li></ul><ul id="2262881f-86f1-80bc-9eed-c23df4d5930c" class="bulleted-list"><li style="list-style-type:disc"><strong>7.3 Report Generation &amp; Custom Templates</strong></li></ul><ul id="2262881f-86f1-8082-bf7e-e80da1542eca" class="bulleted-list"><li style="list-style-type:disc"><strong>7.4 Batch Processing &amp; Multi-Module Scenarios</strong></li></ul><ul id="2262881f-86f1-8089-a673-fd6bfa2457cd" class="bulleted-list"><li style="list-style-type:disc"><strong>7.5 Alerts, Notifications &amp; Approval Workflows</strong></li></ul><ul id="2262881f-86f1-8054-ac6b-c4996f70885d" class="bulleted-list"><li style="list-style-type:disc"><strong>7.6 Accessibility &amp; WCAG Conformance</strong></li></ul><h3 id="2262881f-86f1-8085-8613-f47aac6f4517" class="">Administration &amp; Configuration</h3><p id="2262881f-86f1-80e0-8430-fe5da17968da" class="">Defines permissions, system-wide configuration controls, integrations, and disaster recovery options.</p><ul id="2262881f-86f1-8028-8f26-d770222aa39e" class="bulleted-list"><li style="list-style-type:disc"><strong>8.1 User &amp; Group Management</strong></li></ul><ul id="2262881f-86f1-8024-ac6e-cec5a52b94b1" class="bulleted-list"><li style="list-style-type:disc"><strong>8.2 Permission Profiles &amp; Access Control</strong></li></ul><ul id="2262881f-86f1-80fe-ab60-e2cc07ab7a3a" class="bulleted-list"><li style="list-style-type:disc"><strong>8.3 System Settings, Defaults &amp; Global Parameters</strong></li></ul><ul id="2262881f-86f1-8043-a624-cfd0c5f5eeec" class="bulleted-list"><li style="list-style-type:disc"><strong>8.4 External Integrations (CAD/BIM, ERP, Document Repos)</strong></li></ul><ul id="2262881f-86f1-801b-9e77-c1f4687072a7" class="bulleted-list"><li style="list-style-type:disc"><strong>8.5 Backup, Archiving &amp; Disaster Recovery</strong></li></ul><ul id="2262881f-86f1-8061-8f46-e901b8204fc7" class="bulleted-list"><li style="list-style-type:disc"><strong>8.6 Documentation Change Control Workflow</strong></li></ul><h3 id="2262881f-86f1-8037-a83a-ee3b9fbc75a1" class="">Quality Assurance &amp; Validation</h3><p id="2262881f-86f1-80cc-bdc1-d535c8f82af2" class="">Covers the verification strategy, peer review processes, traceability mechanisms, and audit log practices.</p><ul id="2262881f-86f1-802d-892a-d58a580a1291" class="bulleted-list"><li style="list-style-type:disc"><strong>9.1 Verification &amp; Validation Procedures</strong></li></ul><ul id="2262881f-86f1-80b7-87d8-ef4d26bfc4a6" class="bulleted-list"><li style="list-style-type:disc"><strong>9.2 Cross-Check Tools &amp; Peer Review Templates</strong></li></ul><ul id="2262881f-86f1-804d-8981-fc6c107bf4d5" class="bulleted-list"><li style="list-style-type:disc"><strong>9.3 Audit Logs &amp; Change History</strong></li></ul><ul id="2262881f-86f1-8058-bbf1-cdd24c4ad048" class="bulleted-list"><li style="list-style-type:disc"><strong>9.4 Traceability Matrices (Input ↔ Output)</strong></li></ul><h3 id="2262881f-86f1-80b8-b5c2-e3e0d992dbd2" class="">Training &amp; Onboarding</h3><p id="2262881f-86f1-8074-a15c-d8ec80617b9e" class="">Helps users build proficiency in the tools and documents through structured learning paths.</p><ul id="2262881f-86f1-8016-807c-e507a5593f80" class="bulleted-list"><li style="list-style-type:disc"><strong>10.1 Recommended Learning Paths</strong></li></ul><ul id="2262881f-86f1-808b-833a-de43be1cf7a1" class="bulleted-list"><li style="list-style-type:disc"><strong>10.2 User Guides &amp; Video Tutorials</strong></li></ul><ul id="2262881f-86f1-8073-9841-c9b7458418c4" class="bulleted-list"><li style="list-style-type:disc"><strong>10.3 Certified Power-User Program</strong></li></ul><ul id="2262881f-86f1-801d-811f-d0e5ca91d0ec" class="bulleted-list"><li style="list-style-type:disc"><strong>10.4 Knowledge-Base &amp; FAQs</strong></li></ul><h3 id="2262881f-86f1-8001-92bf-d405ae02fd61" class="">Troubleshooting &amp; Support</h3><p id="2262881f-86f1-804c-8967-e139d8bd622f" class="">Provides escalation paths, diagnostic procedures, and points of contact for technical assistance.</p><ul id="2262881f-86f1-8038-b1d5-dd7e67218a32" class="bulleted-list"><li style="list-style-type:disc"><strong>11.1 Common Error Messages &amp; Resolutions</strong></li></ul><ul id="2262881f-86f1-8016-96b2-f95e88bd8258" class="bulleted-list"><li style="list-style-type:disc"><strong>11.2 Diagnostic Logs &amp; How to Capture Them</strong></li></ul><ul id="2262881f-86f1-8099-a54a-c9b3059765f9" class="bulleted-list"><li style="list-style-type:disc"><strong>11.3 Escalation Procedure &amp; SLAs</strong></li></ul><ul id="2262881f-86f1-8039-bfcf-e773dd768be0" class="bulleted-list"><li style="list-style-type:disc"><strong>11.4 Contacting Technical Support</strong></li></ul><h3 id="2262881f-86f1-8096-b9ed-f452a0323382" class="">Security &amp; Compliance</h3><p id="2262881f-86f1-8054-a6a4-f71eca213aa1" class="">Explains security architecture, privacy enforcement, authentication models, and regulatory conformance practices. Role-based responsibility (e.g., security officer) is described in Section 8.1.</p><ul id="2262881f-86f1-8099-b430-fc8b39d32a1e" class="bulleted-list"><li style="list-style-type:disc"><strong>12.1 Data Encryption &amp; Privacy Controls</strong></li></ul><ul id="************************************" class="bulleted-list"><li style="list-style-type:disc"><strong>12.2 Authentication Methods &amp; Single Sign-On</strong></li></ul><ul id="2262881f-86f1-8005-8a6d-ea3cee7b48b8" class="bulleted-list"><li style="list-style-type:disc"><strong>12.3 Regulatory Compliance (GDPR, Local Codes)</strong></li></ul><ul id="2262881f-86f1-8052-9786-cc5431752e48" class="bulleted-list"><li style="list-style-type:disc"><strong>12.4 Pen-testing &amp; Vulnerability Management</strong></li></ul><h3 id="2262881f-86f1-806c-bf9d-dee6e99e386c" class="">Appendices &amp; Index</h3><p id="2262881f-86f1-8043-9fff-ff800d6c40db" class="">Provides supplemental materials including formulas, standards, glossary terms, and documentation changelogs.</p><ul id="2262881f-86f1-802a-ad76-f4dc0038e109" class="bulleted-list"><li style="list-style-type:disc"><strong>A. Calculation Methodologies &amp; Formulas</strong></li></ul><ul id="2262881f-86f1-8064-a31d-fed58645f61e" class="bulleted-list"><li style="list-style-type:disc"><strong>B. Industry Standards &amp; Code References</strong></li></ul><ul id="2262881f-86f1-80af-ae35-c559d20d8318" class="bulleted-list"><li style="list-style-type:disc"><strong>C. Glossary of Terms &amp; Acronyms</strong></li></ul><ul id="2262881f-86f1-80e9-a9d0-ec134098985f" class="bulleted-list"><li style="list-style-type:disc"><strong>D. Release Notes &amp; Version History</strong></li></ul><ul id="2262881f-86f1-8026-ae71-fc9b7b4fd723" class="bulleted-list"><li style="list-style-type:disc"><strong>E. Sample Project Files &amp; Templates</strong></li></ul><ul id="2262881f-86f1-80d6-83f3-c68536df7fd2" class="bulleted-list"><li style="list-style-type:disc"><strong>Index</strong></li></ul><p id="2262881f-86f1-8007-879d-d9e9de4ec640" class="">This structure supports the core philosophy of SizeWise: modular design, rigorous compliance, and role-based accessibility. As new features and modules are added to the Suite, the documentation will evolve with a consistent and maintainable architecture that ensures both internal coherence and external usability.</p></div></article><span class="sans" style="font-size:14px;padding-top:2em"></span></body></html>