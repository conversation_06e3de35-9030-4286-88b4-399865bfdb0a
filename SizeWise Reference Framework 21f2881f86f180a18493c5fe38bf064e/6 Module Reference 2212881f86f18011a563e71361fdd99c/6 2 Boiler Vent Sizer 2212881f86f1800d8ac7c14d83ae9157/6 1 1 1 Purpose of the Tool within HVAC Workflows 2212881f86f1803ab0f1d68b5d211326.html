<html><head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"/><title>******* Purpose of the Tool within HVAC Workflows</title><style>
/* cspell:disable-file */
/* webkit printing magic: print all background colors */
html {
	-webkit-print-color-adjust: exact;
}
* {
	box-sizing: border-box;
	-webkit-print-color-adjust: exact;
}

html,
body {
	margin: 0;
	padding: 0;
}
@media only screen {
	body {
		margin: 2em auto;
		max-width: 900px;
		color: rgb(55, 53, 47);
	}
}

body {
	line-height: 1.5;
	white-space: pre-wrap;
}

a,
a.visited {
	color: inherit;
	text-decoration: underline;
}

.pdf-relative-link-path {
	font-size: 80%;
	color: #444;
}

h1,
h2,
h3 {
	letter-spacing: -0.01em;
	line-height: 1.2;
	font-weight: 600;
	margin-bottom: 0;
}

.page-title {
	font-size: 2.5rem;
	font-weight: 700;
	margin-top: 0;
	margin-bottom: 0.75em;
}

h1 {
	font-size: 1.875rem;
	margin-top: 1.875rem;
}

h2 {
	font-size: 1.5rem;
	margin-top: 1.5rem;
}

h3 {
	font-size: 1.25rem;
	margin-top: 1.25rem;
}

.source {
	border: 1px solid #ddd;
	border-radius: 3px;
	padding: 1.5em;
	word-break: break-all;
}

.callout {
	border-radius: 3px;
	padding: 1rem;
}

figure {
	margin: 1.25em 0;
	page-break-inside: avoid;
}

figcaption {
	opacity: 0.5;
	font-size: 85%;
	margin-top: 0.5em;
}

mark {
	background-color: transparent;
}

.indented {
	padding-left: 1.5em;
}

hr {
	background: transparent;
	display: block;
	width: 100%;
	height: 1px;
	visibility: visible;
	border: none;
	border-bottom: 1px solid rgba(55, 53, 47, 0.09);
}

img {
	max-width: 100%;
}

@media only print {
	img {
		max-height: 100vh;
		object-fit: contain;
	}
}

@page {
	margin: 1in;
}

.collection-content {
	font-size: 0.875rem;
}

.column-list {
	display: flex;
	justify-content: space-between;
}

.column {
	padding: 0 1em;
}

.column:first-child {
	padding-left: 0;
}

.column:last-child {
	padding-right: 0;
}

.table_of_contents-item {
	display: block;
	font-size: 0.875rem;
	line-height: 1.3;
	padding: 0.125rem;
}

.table_of_contents-indent-1 {
	margin-left: 1.5rem;
}

.table_of_contents-indent-2 {
	margin-left: 3rem;
}

.table_of_contents-indent-3 {
	margin-left: 4.5rem;
}

.table_of_contents-link {
	text-decoration: none;
	opacity: 0.7;
	border-bottom: 1px solid rgba(55, 53, 47, 0.18);
}

table,
th,
td {
	border: 1px solid rgba(55, 53, 47, 0.09);
	border-collapse: collapse;
}

table {
	border-left: none;
	border-right: none;
}

th,
td {
	font-weight: normal;
	padding: 0.25em 0.5em;
	line-height: 1.5;
	min-height: 1.5em;
	text-align: left;
}

th {
	color: rgba(55, 53, 47, 0.6);
}

ol,
ul {
	margin: 0;
	margin-block-start: 0.6em;
	margin-block-end: 0.6em;
}

li > ol:first-child,
li > ul:first-child {
	margin-block-start: 0.6em;
}

ul > li {
	list-style: disc;
}

ul.to-do-list {
	padding-inline-start: 0;
}

ul.to-do-list > li {
	list-style: none;
}

.to-do-children-checked {
	text-decoration: line-through;
	opacity: 0.375;
}

ul.toggle > li {
	list-style: none;
}

ul {
	padding-inline-start: 1.7em;
}

ul > li {
	padding-left: 0.1em;
}

ol {
	padding-inline-start: 1.6em;
}

ol > li {
	padding-left: 0.2em;
}

.mono ol {
	padding-inline-start: 2em;
}

.mono ol > li {
	text-indent: -0.4em;
}

.toggle {
	padding-inline-start: 0em;
	list-style-type: none;
}

/* Indent toggle children */
.toggle > li > details {
	padding-left: 1.7em;
}

.toggle > li > details > summary {
	margin-left: -1.1em;
}

.selected-value {
	display: inline-block;
	padding: 0 0.5em;
	background: rgba(206, 205, 202, 0.5);
	border-radius: 3px;
	margin-right: 0.5em;
	margin-top: 0.3em;
	margin-bottom: 0.3em;
	white-space: nowrap;
}

.collection-title {
	display: inline-block;
	margin-right: 1em;
}

.page-description {
	margin-bottom: 2em;
}

.simple-table {
	margin-top: 1em;
	font-size: 0.875rem;
	empty-cells: show;
}
.simple-table td {
	height: 29px;
	min-width: 120px;
}

.simple-table th {
	height: 29px;
	min-width: 120px;
}

.simple-table-header-color {
	background: rgb(247, 246, 243);
	color: black;
}
.simple-table-header {
	font-weight: 500;
}

time {
	opacity: 0.5;
}

.icon {
	display: inline-block;
	max-width: 1.2em;
	max-height: 1.2em;
	text-decoration: none;
	vertical-align: text-bottom;
	margin-right: 0.5em;
}

img.icon {
	border-radius: 3px;
}

.user-icon {
	width: 1.5em;
	height: 1.5em;
	border-radius: 100%;
	margin-right: 0.5rem;
}

.user-icon-inner {
	font-size: 0.8em;
}

.text-icon {
	border: 1px solid #000;
	text-align: center;
}

.page-cover-image {
	display: block;
	object-fit: cover;
	width: 100%;
	max-height: 30vh;
}

.page-header-icon {
	font-size: 3rem;
	margin-bottom: 1rem;
}

.page-header-icon-with-cover {
	margin-top: -0.72em;
	margin-left: 0.07em;
}

.page-header-icon img {
	border-radius: 3px;
}

.link-to-page {
	margin: 1em 0;
	padding: 0;
	border: none;
	font-weight: 500;
}

p > .user {
	opacity: 0.5;
}

td > .user,
td > time {
	white-space: nowrap;
}

input[type="checkbox"] {
	transform: scale(1.5);
	margin-right: 0.6em;
	vertical-align: middle;
}

p {
	margin-top: 0.5em;
	margin-bottom: 0.5em;
}

.image {
	border: none;
	margin: 1.5em 0;
	padding: 0;
	border-radius: 0;
	text-align: center;
}

.code,
code {
	background: rgba(135, 131, 120, 0.15);
	border-radius: 3px;
	padding: 0.2em 0.4em;
	border-radius: 3px;
	font-size: 85%;
	tab-size: 2;
}

code {
	color: #eb5757;
}

.code {
	padding: 1.5em 1em;
}

.code-wrap {
	white-space: pre-wrap;
	word-break: break-all;
}

.code > code {
	background: none;
	padding: 0;
	font-size: 100%;
	color: inherit;
}

blockquote {
	font-size: 1.25em;
	margin: 1em 0;
	padding-left: 1em;
	border-left: 3px solid rgb(55, 53, 47);
}

.bookmark {
	text-decoration: none;
	max-height: 8em;
	padding: 0;
	display: flex;
	width: 100%;
	align-items: stretch;
}

.bookmark-title {
	font-size: 0.85em;
	overflow: hidden;
	text-overflow: ellipsis;
	height: 1.75em;
	white-space: nowrap;
}

.bookmark-text {
	display: flex;
	flex-direction: column;
}

.bookmark-info {
	flex: 4 1 180px;
	padding: 12px 14px 14px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.bookmark-image {
	width: 33%;
	flex: 1 1 180px;
	display: block;
	position: relative;
	object-fit: cover;
	border-radius: 1px;
}

.bookmark-description {
	color: rgba(55, 53, 47, 0.6);
	font-size: 0.75em;
	overflow: hidden;
	max-height: 4.5em;
	word-break: break-word;
}

.bookmark-href {
	font-size: 0.75em;
	margin-top: 0.25em;
}

.sans { font-family: ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol"; }
.code { font-family: "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace; }
.serif { font-family: Lyon-Text, Georgia, ui-serif, serif; }
.mono { font-family: iawriter-mono, Nitti, Menlo, Courier, monospace; }
.pdf .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK JP'; }
.pdf:lang(zh-CN) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK SC'; }
.pdf:lang(zh-TW) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK TC'; }
.pdf:lang(ko-KR) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK KR'; }
.pdf .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK JP'; }
.pdf:lang(zh-CN) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC'; }
.pdf:lang(zh-TW) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK TC'; }
.pdf:lang(ko-KR) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK KR'; }
.pdf .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK JP'; }
.pdf:lang(zh-CN) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK SC'; }
.pdf:lang(zh-TW) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK TC'; }
.pdf:lang(ko-KR) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK KR'; }
.pdf .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK JP'; }
.pdf:lang(zh-CN) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC'; }
.pdf:lang(zh-TW) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK TC'; }
.pdf:lang(ko-KR) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK KR'; }
.highlight-default {
	color: rgba(50, 48, 44, 1);
}
.highlight-gray {
	color: rgba(115, 114, 110, 1);
	fill: rgba(115, 114, 110, 1);
}
.highlight-brown {
	color: rgba(159, 107, 83, 1);
	fill: rgba(159, 107, 83, 1);
}
.highlight-orange {
	color: rgba(217, 115, 13, 1);
	fill: rgba(217, 115, 13, 1);
}
.highlight-yellow {
	color: rgba(203, 145, 47, 1);
	fill: rgba(203, 145, 47, 1);
}
.highlight-teal {
	color: rgba(68, 131, 97, 1);
	fill: rgba(68, 131, 97, 1);
}
.highlight-blue {
	color: rgba(51, 126, 169, 1);
	fill: rgba(51, 126, 169, 1);
}
.highlight-purple {
	color: rgba(144, 101, 176, 1);
	fill: rgba(144, 101, 176, 1);
}
.highlight-pink {
	color: rgba(193, 76, 138, 1);
	fill: rgba(193, 76, 138, 1);
}
.highlight-red {
	color: rgba(205, 60, 58, 1);
	fill: rgba(205, 60, 58, 1);
}
.highlight-default_background {
	color: rgba(50, 48, 44, 1);
}
.highlight-gray_background {
	background: rgba(248, 248, 247, 1);
}
.highlight-brown_background {
	background: rgba(244, 238, 238, 1);
}
.highlight-orange_background {
	background: rgba(251, 236, 221, 1);
}
.highlight-yellow_background {
	background: rgba(251, 243, 219, 1);
}
.highlight-teal_background {
	background: rgba(237, 243, 236, 1);
}
.highlight-blue_background {
	background: rgba(231, 243, 248, 1);
}
.highlight-purple_background {
	background: rgba(248, 243, 252, 1);
}
.highlight-pink_background {
	background: rgba(252, 241, 246, 1);
}
.highlight-red_background {
	background: rgba(253, 235, 236, 1);
}
.block-color-default {
	color: inherit;
	fill: inherit;
}
.block-color-gray {
	color: rgba(115, 114, 110, 1);
	fill: rgba(115, 114, 110, 1);
}
.block-color-brown {
	color: rgba(159, 107, 83, 1);
	fill: rgba(159, 107, 83, 1);
}
.block-color-orange {
	color: rgba(217, 115, 13, 1);
	fill: rgba(217, 115, 13, 1);
}
.block-color-yellow {
	color: rgba(203, 145, 47, 1);
	fill: rgba(203, 145, 47, 1);
}
.block-color-teal {
	color: rgba(68, 131, 97, 1);
	fill: rgba(68, 131, 97, 1);
}
.block-color-blue {
	color: rgba(51, 126, 169, 1);
	fill: rgba(51, 126, 169, 1);
}
.block-color-purple {
	color: rgba(144, 101, 176, 1);
	fill: rgba(144, 101, 176, 1);
}
.block-color-pink {
	color: rgba(193, 76, 138, 1);
	fill: rgba(193, 76, 138, 1);
}
.block-color-red {
	color: rgba(205, 60, 58, 1);
	fill: rgba(205, 60, 58, 1);
}
.block-color-default_background {
	color: inherit;
	fill: inherit;
}
.block-color-gray_background {
	background: rgba(248, 248, 247, 1);
}
.block-color-brown_background {
	background: rgba(244, 238, 238, 1);
}
.block-color-orange_background {
	background: rgba(251, 236, 221, 1);
}
.block-color-yellow_background {
	background: rgba(251, 243, 219, 1);
}
.block-color-teal_background {
	background: rgba(237, 243, 236, 1);
}
.block-color-blue_background {
	background: rgba(231, 243, 248, 1);
}
.block-color-purple_background {
	background: rgba(248, 243, 252, 1);
}
.block-color-pink_background {
	background: rgba(252, 241, 246, 1);
}
.block-color-red_background {
	background: rgba(253, 235, 236, 1);
}
.select-value-color-default { background-color: rgba(84, 72, 49, 0.08); }
.select-value-color-gray { background-color: rgba(84, 72, 49, 0.15); }
.select-value-color-brown { background-color: rgba(210, 162, 141, 0.35); }
.select-value-color-orange { background-color: rgba(224, 124, 57, 0.27); }
.select-value-color-yellow { background-color: rgba(236, 191, 66, 0.39); }
.select-value-color-green { background-color: rgba(123, 183, 129, 0.27); }
.select-value-color-blue { background-color: rgba(93, 165, 206, 0.27); }
.select-value-color-purple { background-color: rgba(168, 129, 197, 0.27); }
.select-value-color-pink { background-color: rgba(225, 136, 179, 0.27); }
.select-value-color-red { background-color: rgba(244, 171, 159, 0.4); }

.checkbox {
	display: inline-flex;
	vertical-align: text-bottom;
	width: 16;
	height: 16;
	background-size: 16px;
	margin-left: 2px;
	margin-right: 5px;
}

.checkbox-on {
	background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20width%3D%2216%22%20height%3D%2216%22%20fill%3D%22%2358A9D7%22%2F%3E%0A%3Cpath%20d%3D%22M6.71429%2012.2852L14%204.9995L12.7143%203.71436L6.71429%209.71378L3.28571%206.2831L2%207.57092L6.71429%2012.2852Z%22%20fill%3D%22white%22%2F%3E%0A%3C%2Fsvg%3E");
}

.checkbox-off {
	background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20x%3D%220.75%22%20y%3D%220.75%22%20width%3D%2214.5%22%20height%3D%2214.5%22%20fill%3D%22white%22%20stroke%3D%22%2336352F%22%20stroke-width%3D%221.5%22%2F%3E%0A%3C%2Fsvg%3E");
}
	
</style></head><body><article id="2212881f-86f1-803a-b0f1-d68b5d211326" class="page sans"><header><h1 class="page-title">******* Purpose of the Tool within HVAC Workflows</h1><p class="page-description"></p></header><div class="page-body"><hr id="2212881f-86f1-8042-b47c-e55ff50a8e46"/><h3 id="2212881f-86f1-805c-aadf-c246b84f4436" class="">The <strong>Boiler Vent Sizer (BVS)</strong> </h3><blockquote id="2212881f-86f1-80c0-bf5b-c964ad53561a" class="">A standards-aligned, performance-driven module integrated into the <strong>SizeWise Suite</strong>, purpose-built to assist mechanical engineers, HVAC estimators, and system designers in sizing gas vent systems for <strong>Category I–IV appliances</strong>. Designed in accordance with the <strong>National Fuel Gas Code (NFPA 54, 2024 Edition)</strong> and optionally cross-validated with the <strong>International Fuel Gas Code (IFGC, 2024 Edition)</strong>, the tool supports both <strong>prescriptive</strong> and <strong>performance-based</strong> vent sizing methodologies as permitted by current model codes.</blockquote><blockquote id="2212881f-86f1-80d4-ab5b-d06b4482668a" class="">BVS is not merely a calculator—it is a digital design assistant that encapsulates validated engineering logic, model code interpretations, and structured compliance feedback. It provides a deterministic framework for boiler flue design that reduces ambiguity and supports formal documentation for review by <strong>Authorities Having Jurisdiction (AHJs)</strong>, design peers, or insurance underwriters.</blockquote><hr id="2212881f-86f1-80d6-85d7-e35d08408631"/><h3 id="2212881f-86f1-8024-989c-ecb00adbbc9a" class=""><strong>Engineering Risks of Manual Sizing</strong></h3><p id="2212881f-86f1-806f-b19c-d1edac75ad70" class="">Improvised or spreadsheet-based vent design introduces three recurrent failure scenarios:</p><ol type="1" id="2212881f-86f1-8048-860f-e8e5ec30fd4e" class="numbered-list" start="1"><li><strong>Under-sizing</strong> the vent system<ul id="2212881f-86f1-809f-ab0c-fe2df186a38a" class="bulleted-list"><li style="list-style-type:disc">Consequences: excessive static pressure, burner malfunction, flue gas spillage, CO exposure, safety shutdowns</li></ul><ul id="2212881f-86f1-807e-afcf-f5c3b76fc5a2" class="bulleted-list"><li style="list-style-type:disc">Potential outcome: violation of §13.1 (capacity tables) and Annex G (draft verification)</li></ul></li></ol><ol type="1" id="2212881f-86f1-8076-b36a-fd462b071ed4" class="numbered-list" start="2"><li><strong>Over-sizing</strong> the vent system<ul id="2212881f-86f1-807d-9da9-c073aa4aa062" class="bulleted-list"><li style="list-style-type:disc">Consequences: loss of buoyancy, cooled flue gases, internal condensation, corrosion, premature material failure</li></ul><ul id="2212881f-86f1-809a-8598-df660bcd3480" class="bulleted-list"><li style="list-style-type:disc">Often leads to misclassification under NFPA 54 §5.6.7 (requiring special gas venting)</li></ul></li></ol><ol type="1" id="2212881f-86f1-80f7-a6ab-dcc8ff4dc4eb" class="numbered-list" start="3"><li><strong>Non-compliance</strong> with prescriptive rules<ul id="2212881f-86f1-80ac-a358-f17c1853c271" class="bulleted-list"><li style="list-style-type:disc">Examples: insufficient pitch (§5.3.10), wrong clearance-to-combustibles (§5.3.14), unsupported vent lengths (§13.3), or improper material use (positive-pressure appliances using Type B)</li></ul></li></ol><p id="2212881f-86f1-8017-b584-f93807f9fbdf" class="">Each failure risks inspection rejection, rework costs, loss of warranty, or worse—legal liability in post-incident investigations.</p><hr id="2212881f-86f1-8016-af5d-d938b8c45aec"/><h3 id="2212881f-86f1-802e-99dc-d5081b9bd7ea" class=""><strong>Core Functional Safeguards in BVS</strong></h3><p id="2212881f-86f1-80b9-9b3d-cc397d5d7231" class="">The Boiler Vent Sizer eliminates the above risks by enforcing traceable, deterministic logic:</p><ul id="2212881f-86f1-80b8-91cc-efcd3cd77678" class="bulleted-list"><li style="list-style-type:disc">✅ <strong>Parametric inputs with code-anchored defaults</strong><p id="2212881f-86f1-8045-b6ab-f104e62305ee" class="">Every field (e.g., boiler capacity, vent material, ambient air temp, elevation) is unit-aware, internally validated, and defaults to conservative, code-approved assumptions.</p></li></ul><ul id="2212881f-86f1-800e-acb2-cfd3f9c7a541" class="bulleted-list"><li style="list-style-type:disc">✅ <strong>Deterministic vent diameter selection</strong><p id="2212881f-86f1-805e-904c-fbf829e86906" class="">In addition to using NFPA 54 Table 13.1 and IFGC Appendix E, BVS supports <strong>dynamic sizing using Darcy–Weisbach</strong> where tables are not applicable (e.g., unique stack layouts or oversized appliances). It applies manufacturer data when required under §12.7.4.3.</p></li></ul><ul id="2212881f-86f1-808c-8a79-d224d0cc0827" class="bulleted-list"><li style="list-style-type:disc">✅ <strong>Automated NFPA classification (Cat I–IV)</strong><p id="2212881f-86f1-808c-80d9-c1293ad49757" class="">Using real-time logic based on <strong>pressure regime and condensation analysis</strong>, the tool classifies each vent system according to §3.3.49 and §3.3.53.3 of NFPA 54 and adjusts material requirements accordingly.</p></li></ul><ul id="2212881f-86f1-80c3-b809-d976b59f2bb8" class="bulleted-list"><li style="list-style-type:disc">✅ <strong>Dual-mode output</strong><p id="2212881f-86f1-806d-956c-f49bbaee94da" class="">BVS outputs both:</p><ul id="2212881f-86f1-80d2-a736-f50f10b3f42d" class="bulleted-list"><li style="list-style-type:circle"><strong>Design guidance</strong> (recommended diameter, equivalent length, draft pressure, combustion air)</li></ul><ul id="2212881f-86f1-80a4-9998-fdb93d271e15" class="bulleted-list"><li style="list-style-type:circle"><strong>Compliance validation</strong> (pass/fail flags tied to NFPA clauses, dew point checks, flue temperature thresholds, material restrictions)</li></ul></li></ul><p id="2212881f-86f1-806c-994a-d6a91ce77a56" class="">Each result is accompanied by a <strong>justification trace</strong>, improving transparency and reducing the risk of professional disputes or code rejection.</p><hr id="2212881f-86f1-80cf-a48d-c4017c70372e"/><h3 id="2212881f-86f1-808b-b557-cd424478a6f6" class=""><strong>Operational Flexibility</strong></h3><p id="2212881f-86f1-80d1-8cee-d6b745200e39" class="">To ensure it serves engineers in both pre-construction and field environments, the BVS is built as a <strong>modular, offline-capable tool</strong>:</p><table id="2212881f-86f1-8043-b911-dfc382bd2e23" class="simple-table"><tbody><tr id="2212881f-86f1-8020-9d01-ceee6394168c"><td id="@jTh" class=""><strong>Environment</strong></td><td id="lJ\h" class=""><strong>Use Cases</strong></td><td id="}KoT" class="" style="width:325px"><strong>Features</strong></td></tr><tr id="2212881f-86f1-803f-a79f-f8f1d0125dc6"><td id="@jTh" class="">💼<strong>Office/Design</strong></td><td id="lJ\h" class="">Project planning &amp; design</td><td id="}KoT" class="" style="width:325px">·Integration with BIM platforms (e.g., Revit, AutoCAD MEP)·Automated BOQ generation aligned with project-specific equipment·Cross-referencing with other SizeWise modules</td></tr><tr id="2212881f-86f1-80e0-a88b-dc372eedb6b6"><td id="@jTh" class="">🛠️<strong>Field/Commissioning</strong></td><td id="lJ\h" class="">Installation &amp; verification</td><td id="}KoT" class="" style="width:325px">·Usable via browser in offline mode for as-built validation·Built-in checks for<strong>IFGC Appendix D</strong>field tests·Allows project teams to document deviations and generate reports</td></tr></tbody></table><hr id="2212881f-86f1-807a-9f43-ecf8f73655df"/><h3 id="2212881f-86f1-80d2-9e11-d01d6be8f3dc" class=""><strong>Future-Proof Design</strong></h3><p id="2212881f-86f1-800c-83fb-fb69a560321b" class="">The BVS’s modular architecture enables:</p><ul id="2212881f-86f1-8056-a26d-f0e9a7b7f6a5" class="bulleted-list"><li style="list-style-type:disc">Adaptation to <strong>regional codes</strong> (e.g., CSA B149.1 in Canada, EN 13384 in EU, or BPS Fuel Gas Code for the Philippines)</li></ul><ul id="2212881f-86f1-8048-a2e7-c174f6f43018" class="bulleted-list"><li style="list-style-type:disc">Extension to <strong>multi-boiler systems</strong>, <strong>fan-assisted venting</strong>, or <strong>stacked appliance arrays</strong></li></ul><ul id="2212881f-86f1-80c5-940e-dbd46e5346c8" class="bulleted-list"><li style="list-style-type:disc">Integration with <strong>ERP/Procurement systems</strong> for automated ordering workflows</li></ul><p id="2212881f-86f1-808a-80dc-cbaab8e9918a" class="">All sizing logic is encapsulated in <strong>test-driven Python modules</strong>, each of which is accompanied by:</p><ul id="2212881f-86f1-80cc-ba51-e0c6f40cea07" class="bulleted-list"><li style="list-style-type:disc">Docstring-based unit documentation</li></ul><ul id="2212881f-86f1-802a-bef2-e8dd44cc5006" class="bulleted-list"><li style="list-style-type:disc">Clause references to NFPA 54/IFGC</li></ul><ul id="2212881f-86f1-8095-bfd9-f5368780d900" class="bulleted-list"><li style="list-style-type:disc">Unit-tested edge-case handling</li></ul><ul id="2212881f-86f1-80d0-b104-f3fe1812147f" class="bulleted-list"><li style="list-style-type:disc">Automatic documentation generation (Sphinx-ready)</li></ul><hr id="2212881f-86f1-80c3-91e8-c9fea50eda05"/><h3 id="2212881f-86f1-8011-8ec0-d6be5fc91be5" class=""><strong>Legal Risk Mitigation Statement</strong></h3><p id="2212881f-86f1-8012-8d2b-d4a40f772857" class="">To minimize liability exposure for all parties:</p><ul id="2212881f-86f1-8053-842e-cd16ba7881bb" class="bulleted-list"><li style="list-style-type:disc">All NFPA 54 clauses are referenced explicitly in <strong>standards_validation.py</strong>, and regularly reviewed against updated editions.</li></ul><ul id="2212881f-86f1-80ab-b0ce-d81a89f831a6" class="bulleted-list"><li style="list-style-type:disc">All vent classification decisions (Category I–IV) follow <strong>NFPA-prescribed logic</strong> without override unless manufacturer data explicitly allows it (per §12.7.4.3).</li></ul><ul id="2212881f-86f1-80f3-942e-d016c5a24b03" class="bulleted-list"><li style="list-style-type:disc">All assumptions (e.g., steady-state firing, dry vent interiors, no fan assist) are <strong>disclosed in the documentation</strong> and visible on result reports.</li></ul><ul id="2212881f-86f1-80c6-96d9-cfeee189a65b" class="bulleted-list"><li style="list-style-type:disc">Engineers remain responsible for <strong>final design acceptance</strong> under prevailing local codes and AHJ interpretations. The BVS serves as a <strong>tool for decision support, not a substitute for engineering judgement</strong>.</li></ul><hr id="2212881f-86f1-80fa-96f4-d98d7998df91"/><h3 id="2212881f-86f1-80be-b816-d2d66db9dc2f" class=""><strong>Industry Context and Workflow Relevance</strong></h3><p id="2212881f-86f1-8017-8bb6-d82ba49fc556" class="">Boiler vent sizing is not an isolated discipline. It exists at the intersection of:</p><ul id="2212881f-86f1-800e-8c3e-c0b2871cbb73" class="bulleted-list"><li style="list-style-type:disc"><strong>Mechanical load planning</strong></li></ul><ul id="2212881f-86f1-805a-ba04-e93fff2571ee" class="bulleted-list"><li style="list-style-type:disc"><strong>Regulatory compliance</strong></li></ul><ul id="2212881f-86f1-8080-8aa5-e52f630860a6" class="bulleted-list"><li style="list-style-type:disc"><strong>Energy modeling</strong></li></ul><ul id="2212881f-86f1-80b9-8a3f-fa862448b0e4" class="bulleted-list"><li style="list-style-type:disc"><strong>Architectural and structural coordination</strong></li></ul><ul id="2212881f-86f1-801a-9103-f2ac047edb35" class="bulleted-list"><li style="list-style-type:disc"><strong>Product selection and procurement</strong></li></ul><p id="2212881f-86f1-8074-9bdb-ddefe5d2b9c2" class="">Despite this central role, the industry continues to depend on <strong>manual methods</strong>, which often lead to inconsistent results and compliance risks. These methods include:</p><ul id="2212881f-86f1-8015-9b22-f1ae3a76fa51" class="bulleted-list"><li style="list-style-type:disc">Referring to <strong>static NFPA 54 tables</strong> without dynamic adjustments</li></ul><ul id="2212881f-86f1-8029-92a7-ed7d7d550bdb" class="bulleted-list"><li style="list-style-type:disc">Estimating <strong>elbow penalties and developed length</strong> through outdated rules of thumb</li></ul><ul id="2212881f-86f1-80b0-ab52-c04df0e8cdcf" class="bulleted-list"><li style="list-style-type:disc">Applying <strong>altitude and temperature corrections</strong> with varying rigor</li></ul><ul id="2212881f-86f1-805b-9073-eb4c5c31c66e" class="bulleted-list"><li style="list-style-type:disc">Combining disparate tools—Excel sheets, PDF tables, and proprietary product catalogs—with <strong>no automated traceability</strong></li></ul></div></article><span class="sans" style="font-size:14px;padding-top:2em"></span></body></html>