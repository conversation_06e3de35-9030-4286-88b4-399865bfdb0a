<html><head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"/><title>6.2 Boiler Vent Sizer</title><style>
/* cspell:disable-file */
/* webkit printing magic: print all background colors */
html {
	-webkit-print-color-adjust: exact;
}
* {
	box-sizing: border-box;
	-webkit-print-color-adjust: exact;
}

html,
body {
	margin: 0;
	padding: 0;
}
@media only screen {
	body {
		margin: 2em auto;
		max-width: 900px;
		color: rgb(55, 53, 47);
	}
}

body {
	line-height: 1.5;
	white-space: pre-wrap;
}

a,
a.visited {
	color: inherit;
	text-decoration: underline;
}

.pdf-relative-link-path {
	font-size: 80%;
	color: #444;
}

h1,
h2,
h3 {
	letter-spacing: -0.01em;
	line-height: 1.2;
	font-weight: 600;
	margin-bottom: 0;
}

.page-title {
	font-size: 2.5rem;
	font-weight: 700;
	margin-top: 0;
	margin-bottom: 0.75em;
}

h1 {
	font-size: 1.875rem;
	margin-top: 1.875rem;
}

h2 {
	font-size: 1.5rem;
	margin-top: 1.5rem;
}

h3 {
	font-size: 1.25rem;
	margin-top: 1.25rem;
}

.source {
	border: 1px solid #ddd;
	border-radius: 3px;
	padding: 1.5em;
	word-break: break-all;
}

.callout {
	border-radius: 3px;
	padding: 1rem;
}

figure {
	margin: 1.25em 0;
	page-break-inside: avoid;
}

figcaption {
	opacity: 0.5;
	font-size: 85%;
	margin-top: 0.5em;
}

mark {
	background-color: transparent;
}

.indented {
	padding-left: 1.5em;
}

hr {
	background: transparent;
	display: block;
	width: 100%;
	height: 1px;
	visibility: visible;
	border: none;
	border-bottom: 1px solid rgba(55, 53, 47, 0.09);
}

img {
	max-width: 100%;
}

@media only print {
	img {
		max-height: 100vh;
		object-fit: contain;
	}
}

@page {
	margin: 1in;
}

.collection-content {
	font-size: 0.875rem;
}

.column-list {
	display: flex;
	justify-content: space-between;
}

.column {
	padding: 0 1em;
}

.column:first-child {
	padding-left: 0;
}

.column:last-child {
	padding-right: 0;
}

.table_of_contents-item {
	display: block;
	font-size: 0.875rem;
	line-height: 1.3;
	padding: 0.125rem;
}

.table_of_contents-indent-1 {
	margin-left: 1.5rem;
}

.table_of_contents-indent-2 {
	margin-left: 3rem;
}

.table_of_contents-indent-3 {
	margin-left: 4.5rem;
}

.table_of_contents-link {
	text-decoration: none;
	opacity: 0.7;
	border-bottom: 1px solid rgba(55, 53, 47, 0.18);
}

table,
th,
td {
	border: 1px solid rgba(55, 53, 47, 0.09);
	border-collapse: collapse;
}

table {
	border-left: none;
	border-right: none;
}

th,
td {
	font-weight: normal;
	padding: 0.25em 0.5em;
	line-height: 1.5;
	min-height: 1.5em;
	text-align: left;
}

th {
	color: rgba(55, 53, 47, 0.6);
}

ol,
ul {
	margin: 0;
	margin-block-start: 0.6em;
	margin-block-end: 0.6em;
}

li > ol:first-child,
li > ul:first-child {
	margin-block-start: 0.6em;
}

ul > li {
	list-style: disc;
}

ul.to-do-list {
	padding-inline-start: 0;
}

ul.to-do-list > li {
	list-style: none;
}

.to-do-children-checked {
	text-decoration: line-through;
	opacity: 0.375;
}

ul.toggle > li {
	list-style: none;
}

ul {
	padding-inline-start: 1.7em;
}

ul > li {
	padding-left: 0.1em;
}

ol {
	padding-inline-start: 1.6em;
}

ol > li {
	padding-left: 0.2em;
}

.mono ol {
	padding-inline-start: 2em;
}

.mono ol > li {
	text-indent: -0.4em;
}

.toggle {
	padding-inline-start: 0em;
	list-style-type: none;
}

/* Indent toggle children */
.toggle > li > details {
	padding-left: 1.7em;
}

.toggle > li > details > summary {
	margin-left: -1.1em;
}

.selected-value {
	display: inline-block;
	padding: 0 0.5em;
	background: rgba(206, 205, 202, 0.5);
	border-radius: 3px;
	margin-right: 0.5em;
	margin-top: 0.3em;
	margin-bottom: 0.3em;
	white-space: nowrap;
}

.collection-title {
	display: inline-block;
	margin-right: 1em;
}

.page-description {
	margin-bottom: 2em;
}

.simple-table {
	margin-top: 1em;
	font-size: 0.875rem;
	empty-cells: show;
}
.simple-table td {
	height: 29px;
	min-width: 120px;
}

.simple-table th {
	height: 29px;
	min-width: 120px;
}

.simple-table-header-color {
	background: rgb(247, 246, 243);
	color: black;
}
.simple-table-header {
	font-weight: 500;
}

time {
	opacity: 0.5;
}

.icon {
	display: inline-block;
	max-width: 1.2em;
	max-height: 1.2em;
	text-decoration: none;
	vertical-align: text-bottom;
	margin-right: 0.5em;
}

img.icon {
	border-radius: 3px;
}

.user-icon {
	width: 1.5em;
	height: 1.5em;
	border-radius: 100%;
	margin-right: 0.5rem;
}

.user-icon-inner {
	font-size: 0.8em;
}

.text-icon {
	border: 1px solid #000;
	text-align: center;
}

.page-cover-image {
	display: block;
	object-fit: cover;
	width: 100%;
	max-height: 30vh;
}

.page-header-icon {
	font-size: 3rem;
	margin-bottom: 1rem;
}

.page-header-icon-with-cover {
	margin-top: -0.72em;
	margin-left: 0.07em;
}

.page-header-icon img {
	border-radius: 3px;
}

.link-to-page {
	margin: 1em 0;
	padding: 0;
	border: none;
	font-weight: 500;
}

p > .user {
	opacity: 0.5;
}

td > .user,
td > time {
	white-space: nowrap;
}

input[type="checkbox"] {
	transform: scale(1.5);
	margin-right: 0.6em;
	vertical-align: middle;
}

p {
	margin-top: 0.5em;
	margin-bottom: 0.5em;
}

.image {
	border: none;
	margin: 1.5em 0;
	padding: 0;
	border-radius: 0;
	text-align: center;
}

.code,
code {
	background: rgba(135, 131, 120, 0.15);
	border-radius: 3px;
	padding: 0.2em 0.4em;
	border-radius: 3px;
	font-size: 85%;
	tab-size: 2;
}

code {
	color: #eb5757;
}

.code {
	padding: 1.5em 1em;
}

.code-wrap {
	white-space: pre-wrap;
	word-break: break-all;
}

.code > code {
	background: none;
	padding: 0;
	font-size: 100%;
	color: inherit;
}

blockquote {
	font-size: 1.25em;
	margin: 1em 0;
	padding-left: 1em;
	border-left: 3px solid rgb(55, 53, 47);
}

.bookmark {
	text-decoration: none;
	max-height: 8em;
	padding: 0;
	display: flex;
	width: 100%;
	align-items: stretch;
}

.bookmark-title {
	font-size: 0.85em;
	overflow: hidden;
	text-overflow: ellipsis;
	height: 1.75em;
	white-space: nowrap;
}

.bookmark-text {
	display: flex;
	flex-direction: column;
}

.bookmark-info {
	flex: 4 1 180px;
	padding: 12px 14px 14px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.bookmark-image {
	width: 33%;
	flex: 1 1 180px;
	display: block;
	position: relative;
	object-fit: cover;
	border-radius: 1px;
}

.bookmark-description {
	color: rgba(55, 53, 47, 0.6);
	font-size: 0.75em;
	overflow: hidden;
	max-height: 4.5em;
	word-break: break-word;
}

.bookmark-href {
	font-size: 0.75em;
	margin-top: 0.25em;
}

.sans { font-family: ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol"; }
.code { font-family: "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace; }
.serif { font-family: Lyon-Text, Georgia, ui-serif, serif; }
.mono { font-family: iawriter-mono, Nitti, Menlo, Courier, monospace; }
.pdf .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK JP'; }
.pdf:lang(zh-CN) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK SC'; }
.pdf:lang(zh-TW) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK TC'; }
.pdf:lang(ko-KR) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK KR'; }
.pdf .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK JP'; }
.pdf:lang(zh-CN) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC'; }
.pdf:lang(zh-TW) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK TC'; }
.pdf:lang(ko-KR) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK KR'; }
.pdf .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK JP'; }
.pdf:lang(zh-CN) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK SC'; }
.pdf:lang(zh-TW) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK TC'; }
.pdf:lang(ko-KR) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK KR'; }
.pdf .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK JP'; }
.pdf:lang(zh-CN) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC'; }
.pdf:lang(zh-TW) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK TC'; }
.pdf:lang(ko-KR) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK KR'; }
.highlight-default {
	color: rgba(50, 48, 44, 1);
}
.highlight-gray {
	color: rgba(115, 114, 110, 1);
	fill: rgba(115, 114, 110, 1);
}
.highlight-brown {
	color: rgba(159, 107, 83, 1);
	fill: rgba(159, 107, 83, 1);
}
.highlight-orange {
	color: rgba(217, 115, 13, 1);
	fill: rgba(217, 115, 13, 1);
}
.highlight-yellow {
	color: rgba(203, 145, 47, 1);
	fill: rgba(203, 145, 47, 1);
}
.highlight-teal {
	color: rgba(68, 131, 97, 1);
	fill: rgba(68, 131, 97, 1);
}
.highlight-blue {
	color: rgba(51, 126, 169, 1);
	fill: rgba(51, 126, 169, 1);
}
.highlight-purple {
	color: rgba(144, 101, 176, 1);
	fill: rgba(144, 101, 176, 1);
}
.highlight-pink {
	color: rgba(193, 76, 138, 1);
	fill: rgba(193, 76, 138, 1);
}
.highlight-red {
	color: rgba(205, 60, 58, 1);
	fill: rgba(205, 60, 58, 1);
}
.highlight-default_background {
	color: rgba(50, 48, 44, 1);
}
.highlight-gray_background {
	background: rgba(248, 248, 247, 1);
}
.highlight-brown_background {
	background: rgba(244, 238, 238, 1);
}
.highlight-orange_background {
	background: rgba(251, 236, 221, 1);
}
.highlight-yellow_background {
	background: rgba(251, 243, 219, 1);
}
.highlight-teal_background {
	background: rgba(237, 243, 236, 1);
}
.highlight-blue_background {
	background: rgba(231, 243, 248, 1);
}
.highlight-purple_background {
	background: rgba(248, 243, 252, 1);
}
.highlight-pink_background {
	background: rgba(252, 241, 246, 1);
}
.highlight-red_background {
	background: rgba(253, 235, 236, 1);
}
.block-color-default {
	color: inherit;
	fill: inherit;
}
.block-color-gray {
	color: rgba(115, 114, 110, 1);
	fill: rgba(115, 114, 110, 1);
}
.block-color-brown {
	color: rgba(159, 107, 83, 1);
	fill: rgba(159, 107, 83, 1);
}
.block-color-orange {
	color: rgba(217, 115, 13, 1);
	fill: rgba(217, 115, 13, 1);
}
.block-color-yellow {
	color: rgba(203, 145, 47, 1);
	fill: rgba(203, 145, 47, 1);
}
.block-color-teal {
	color: rgba(68, 131, 97, 1);
	fill: rgba(68, 131, 97, 1);
}
.block-color-blue {
	color: rgba(51, 126, 169, 1);
	fill: rgba(51, 126, 169, 1);
}
.block-color-purple {
	color: rgba(144, 101, 176, 1);
	fill: rgba(144, 101, 176, 1);
}
.block-color-pink {
	color: rgba(193, 76, 138, 1);
	fill: rgba(193, 76, 138, 1);
}
.block-color-red {
	color: rgba(205, 60, 58, 1);
	fill: rgba(205, 60, 58, 1);
}
.block-color-default_background {
	color: inherit;
	fill: inherit;
}
.block-color-gray_background {
	background: rgba(248, 248, 247, 1);
}
.block-color-brown_background {
	background: rgba(244, 238, 238, 1);
}
.block-color-orange_background {
	background: rgba(251, 236, 221, 1);
}
.block-color-yellow_background {
	background: rgba(251, 243, 219, 1);
}
.block-color-teal_background {
	background: rgba(237, 243, 236, 1);
}
.block-color-blue_background {
	background: rgba(231, 243, 248, 1);
}
.block-color-purple_background {
	background: rgba(248, 243, 252, 1);
}
.block-color-pink_background {
	background: rgba(252, 241, 246, 1);
}
.block-color-red_background {
	background: rgba(253, 235, 236, 1);
}
.select-value-color-default { background-color: rgba(84, 72, 49, 0.08); }
.select-value-color-gray { background-color: rgba(84, 72, 49, 0.15); }
.select-value-color-brown { background-color: rgba(210, 162, 141, 0.35); }
.select-value-color-orange { background-color: rgba(224, 124, 57, 0.27); }
.select-value-color-yellow { background-color: rgba(236, 191, 66, 0.39); }
.select-value-color-green { background-color: rgba(123, 183, 129, 0.27); }
.select-value-color-blue { background-color: rgba(93, 165, 206, 0.27); }
.select-value-color-purple { background-color: rgba(168, 129, 197, 0.27); }
.select-value-color-pink { background-color: rgba(225, 136, 179, 0.27); }
.select-value-color-red { background-color: rgba(244, 171, 159, 0.4); }

.checkbox {
	display: inline-flex;
	vertical-align: text-bottom;
	width: 16;
	height: 16;
	background-size: 16px;
	margin-left: 2px;
	margin-right: 5px;
}

.checkbox-on {
	background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20width%3D%2216%22%20height%3D%2216%22%20fill%3D%22%2358A9D7%22%2F%3E%0A%3Cpath%20d%3D%22M6.71429%2012.2852L14%204.9995L12.7143%203.71436L6.71429%209.71378L3.28571%206.2831L2%207.57092L6.71429%2012.2852Z%22%20fill%3D%22white%22%2F%3E%0A%3C%2Fsvg%3E");
}

.checkbox-off {
	background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20x%3D%220.75%22%20y%3D%220.75%22%20width%3D%2214.5%22%20height%3D%2214.5%22%20fill%3D%22white%22%20stroke%3D%22%2336352F%22%20stroke-width%3D%221.5%22%2F%3E%0A%3C%2Fsvg%3E");
}
	
</style></head><body><article id="2212881f-86f1-800d-8ac7-c14d83ae9157" class="page sans"><header><div class="page-header-icon undefined"><img class="icon" src="6%202%20Boiler%20Vent%20Sizer%202212881f86f1800d8ac7c14d83ae9157/bfc6d2f0ff502f26157c06b7df943049e301c2b2cc7bc05ac8c0c5ceeca5dac4.png"/></div><h1 class="page-title"><strong>6.2 Boiler Vent Sizer</strong></h1><p class="page-description"></p></header><div class="page-body"><figure id="2212881f-86f1-803a-b0f1-d68b5d211326" class="link-to-page"><a href="6%202%20Boiler%20Vent%20Sizer%202212881f86f1800d8ac7c14d83ae9157/6%201%201%201%20Purpose%20of%20the%20Tool%20within%20HVAC%20Workflows%202212881f86f1803ab0f1d68b5d211326.html">******* Purpose of the Tool within HVAC Workflows</a></figure><hr id="2212881f-86f1-80f2-9f14-c9d0837201f3"/><figure id="2212881f-86f1-80e2-924d-fb4239e2e125" class="link-to-page"><a href="6%202%20Boiler%20Vent%20Sizer%202212881f86f1800d8ac7c14d83ae9157/6%201%202%20Fuel%20Types%20&amp;%20Combustion%20Data%202212881f86f180e2924dfb4239e2e125.html">6.1.2 Fuel Types &amp; Combustion Data</a></figure><hr id="2212881f-86f1-806a-8d7b-dc5f4aacbace"/><figure id="2212881f-86f1-802e-9a72-dbb714e24930" class="link-to-page"><a href="6%202%20Boiler%20Vent%20Sizer%202212881f86f1800d8ac7c14d83ae9157/6%201%203%20Stack%20Height%20&amp;%20Diameter%20Calculations%202212881f86f1802e9a72dbb714e24930.html">6.1.3 Stack Height &amp; Diameter Calculations</a></figure><hr id="2212881f-86f1-808f-9d30-ffb140859cc1"/><figure id="2212881f-86f1-80a8-bcd5-e4673ae045a2" class="link-to-page"><a href="6%202%20Boiler%20Vent%20Sizer%202212881f86f1800d8ac7c14d83ae9157/6%201%204%20Draft%20&amp;%20Temperature%20Considerations%202212881f86f180a8bcd5e4673ae045a2.html">6.1.4 Draft &amp; Temperature Considerations</a></figure><hr id="2212881f-86f1-80a8-85a1-dc83ca76fc4b"/><figure id="2212881f-86f1-809d-8c47-fff2fffdf4fc" class="link-to-page"><a href="6%202%20Boiler%20Vent%20Sizer%202212881f86f1800d8ac7c14d83ae9157/6%201%205%20Sample%20Calculation%20&amp;%20Verification%202212881f86f1809d8c47fff2fffdf4fc.html">6.1.5 Sample Calculation &amp; Verification</a></figure><h3 id="22b2881f-86f1-8006-ace1-e1ff8ecc28af" class="">6.2.1 User Interface &amp; Input Parameters</h3><p id="22b2881f-86f1-8049-84f7-ca789f508487" class="">Based on the content in your document, I can suggest a complete subsection for &quot;6.2 Boiler Vent Sizer&quot; that follows the structure and style of the existing sections. Here&#x27;s what a comprehensive subsection might include:</p><h2 id="22b2881f-86f1-80c2-b931-e35299265271" class="">6.2 Boiler Vent Sizer Implementation</h2><h3 id="22b2881f-86f1-8093-9658-dc7b2552548e" class="">6.2.1 User Interface &amp; Input Parameters</h3><ul id="22b2881f-86f1-808d-9b54-e9be37be74a2" class="bulleted-list"><li style="list-style-type:disc">Boiler specifications (capacity, efficiency, fuel type)</li></ul><ul id="22b2881f-86f1-803d-818a-c0ea8f85ccf9" class="bulleted-list"><li style="list-style-type:disc">Vent configuration (material, length, fittings)</li></ul><ul id="22b2881f-86f1-805c-a813-f5f81ab0070a" class="bulleted-list"><li style="list-style-type:disc">Installation parameters (altitude, ambient temperature)</li></ul><ul id="22b2881f-86f1-8025-aeb5-d19888b642ad" class="bulleted-list"><li style="list-style-type:disc">Code selection (NFPA 54/IFGC) and version</li></ul><h3 id="22b2881f-86f1-80f2-9c96-d795a8e257d2" class="">6.2.2 Calculation Methodology</h3><ul id="22b2881f-86f1-8019-8c9b-c37fc8128606" class="bulleted-list"><li style="list-style-type:disc">Automated appliance categorization (I-IV)</li></ul><ul id="22b2881f-86f1-80fe-8dbb-e5ce3c2974df" class="bulleted-list"><li style="list-style-type:disc">Dynamic sizing with Darcy-Weisbach equations</li></ul><ul id="22b2881f-86f1-80a0-a388-c3ffb93b574a" class="bulleted-list"><li style="list-style-type:disc">Condensation risk analysis and dew point calculations</li></ul><ul id="22b2881f-86f1-801f-ac55-ce53456d8a0b" class="bulleted-list"><li style="list-style-type:disc">Pressure regime evaluation (negative vs. positive)</li></ul><h3 id="22b2881f-86f1-80f0-bf58-f9ffdcc1ac9e" class="">6.2.3 Output Documentation</h3><ul id="22b2881f-86f1-80a4-84d5-ed7c63c5e616" class="bulleted-list"><li style="list-style-type:disc">Recommended vent diameter and material</li></ul><ul id="22b2881f-86f1-809a-9268-f55663e055ea" class="bulleted-list"><li style="list-style-type:disc">Compliance validation report with code references</li></ul><ul id="22b2881f-86f1-8035-9d12-e10b328ec00a" class="bulleted-list"><li style="list-style-type:disc">Justification trace for each design decision</li></ul><ul id="22b2881f-86f1-8089-8cc1-e7e2dc99c280" class="bulleted-list"><li style="list-style-type:disc">Integration points with other SizeWise modules</li></ul><h3 id="22b2881f-86f1-8006-8b39-fbac1e1b564b" class="">6.2.4 Field Application &amp; Verification</h3><ul id="22b2881f-86f1-808c-8c28-d9182f0f1431" class="bulleted-list"><li style="list-style-type:disc">As-built validation procedures</li></ul><ul id="22b2881f-86f1-8046-a93a-d6e25effd5f7" class="bulleted-list"><li style="list-style-type:disc">Draft testing protocols (IFGC Appendix D)</li></ul><ul id="22b2881f-86f1-803a-9bfe-c94bfcf78438" class="bulleted-list"><li style="list-style-type:disc">Troubleshooting guidance for common installation issues</li></ul><h3 id="22b2881f-86f1-80a2-b969-f69b6c731643" class="">6.2.5 Case Studies &amp; Practical Examples</h3><ul id="22b2881f-86f1-80bd-b16c-d4dfc978ac23" class="bulleted-list"><li style="list-style-type:disc">Single boiler residential installation (200 MBH example)</li></ul><ul id="22b2881f-86f1-80b6-86d5-c5af93ed4387" class="bulleted-list"><li style="list-style-type:disc">Commercial multi-boiler system configuration</li></ul><ul id="22b2881f-86f1-809c-a9a9-d4d0628a48d1" class="bulleted-list"><li style="list-style-type:disc">Challenging installations (extreme altitude, condensing systems)</li></ul><p id="22c2881f-86f1-8064-bcf8-c9382fc8dbf9" class="">
</p><p id="22c2881f-86f1-80d9-adf4-c4e9c6817cb6" class=""><strong>6.2 Boiler Vent Sizer (BVS)</strong></p><figure id="22c2881f-86f1-8011-aa32-cf8d7ee86d35" class="link-to-page"><a href="6%202%20Boiler%20Vent%20Sizer%202212881f86f1800d8ac7c14d83ae9157/6%202%201%20What%20is%20the%20BVS%20Tool%2022c2881f86f18011aa32cf8d7ee86d35.html">6.2.1 What is the BVS Tool?</a></figure><figure id="22c2881f-86f1-8062-ac34-ea9d9d4388fc" class="link-to-page"><a href="6%202%20Boiler%20Vent%20Sizer%202212881f86f1800d8ac7c14d83ae9157/6%202%201%201%20Purpose%20&amp;%20Goals%2022c2881f86f18062ac34ea9d9d4388fc.html">6.2.1.1 Purpose &amp; Goals</a></figure><ul id="22c2881f-86f1-8032-acc2-d90720505a87" class="bulleted-list"><li style="list-style-type:disc">Why BVS exists in SizeWise Suite</li></ul><ul id="22c2881f-86f1-803a-bc9b-f3b60abdd00c" class="bulleted-list"><li style="list-style-type:disc">Primary objectives: code-compliant vent sizing for gas (and oil) boilers</li></ul><p id="22c2881f-86f1-8084-993d-cd6d15c5da91" class=""><strong>6.2.1.2 Capabilities &amp; Benefits</strong></p><ul id="22c2881f-86f1-80bc-b5fd-d957626b2167" class="bulleted-list"><li style="list-style-type:disc">Single-appliance vs. common-vent sizing</li></ul><ul id="22c2881f-86f1-8006-8415-c35d398f65a4" class="bulleted-list"><li style="list-style-type:disc">Supports natural gas, propane, oil-fired boilers</li></ul><ul id="22c2881f-86f1-80d0-bc14-d22bc5417930" class="bulleted-list"><li style="list-style-type:disc">Automates Category I–IV classification, draft &amp; condensation checks</li></ul><ul id="22c2881f-86f1-80d6-8ace-feb81623d27c" class="bulleted-list"><li style="list-style-type:disc">Key user benefits: speed, repeatability, built-in QA</li></ul><p id="22c2881f-86f1-8041-8759-fb0825c2e367" class=""><strong>6.2.2 How the Tool Looks &amp; Functions</strong></p><p id="22c2881f-86f1-8049-8b8b-e229b2a3e587" class=""><strong>6.2.2.1 User Interface Overview</strong></p><ul id="22c2881f-86f1-80b4-82a2-d9ac5b51762a" class="bulleted-list"><li style="list-style-type:disc">Screen layout: inputs panel, results window, diagram canvas</li></ul><ul id="22c2881f-86f1-804a-a851-ea6350302a04" class="bulleted-list"><li style="list-style-type:disc">Drill-in navigation (tabs or wizard steps)</li></ul><p id="22c2881f-86f1-8088-9083-f2fe59bc4018" class=""><strong>6.2.2.2 Core Functional Modules</strong></p><ol type="1" id="22c2881f-86f1-80b9-9f09-c4ded4ee869e" class="numbered-list" start="1"><li>Data Entry – boiler specs, site &amp; vent parameters</li></ol><ol type="1" id="22c2881f-86f1-802d-92b7-fdcbcd9a7cd6" class="numbered-list" start="2"><li>Classification – Cat I–IV logic</li></ol><ol type="1" id="22c2881f-86f1-80ec-8c1b-d84d6d8b161c" class="numbered-list" start="3"><li>Sizing Engine – NFPA 54 tables or Darcy–Weisbach</li></ol><ol type="1" id="22c2881f-86f1-8096-a6ab-e04f322d0562" class="numbered-list" start="4"><li>Validation Suite – code checks, warnings</li></ol><ol type="1" id="22c2881f-86f1-8050-8b1a-ca9a6df7d0e7" class="numbered-list" start="5"><li>Exports – report &amp; drawing outputs</li></ol><p id="22c2881f-86f1-8044-9d89-cbfdede468ce" class=""><strong>******* What You Can Do</strong></p><ul id="22c2881f-86f1-807f-b18e-d01a081834b8" class="bulleted-list"><li style="list-style-type:disc">Enter multiple scenarios side-by-side</li></ul><ul id="22c2881f-86f1-8077-ab1e-f6e074109d13" class="bulleted-list"><li style="list-style-type:disc">Toggle NFPA vs. IFGC logic</li></ul><ul id="22c2881f-86f1-802d-a9f5-ced154980907" class="bulleted-list"><li style="list-style-type:disc">Preview 2D vent layout</li></ul><ul id="22c2881f-86f1-800e-af73-eb931b5b8575" class="bulleted-list"><li style="list-style-type:disc">Save &amp; resume offline</li></ul><p id="22c2881f-86f1-807b-b495-ddb273a06c34" class=""><strong>6.2.3 How It Gets Its Answers</strong></p><p id="22c2881f-86f1-80c3-802c-c2be36c863b4" class=""><strong>******* Standards &amp; Logic Sources</strong></p><ul id="22c2881f-86f1-80fe-92f5-f7a0360b9bd0" class="bulleted-list"><li style="list-style-type:disc">NFPA 54–2021 Chapters 12–13 (§12.7-12.12, §13.1-13.3)</li></ul><ul id="22c2881f-86f1-80ef-9a9e-f584c4b6fcfd" class="bulleted-list"><li style="list-style-type:disc">IFGC 2021 Appendix D draft testing</li></ul><ul id="22c2881f-86f1-804e-b11f-ce99517c9142" class="bulleted-list"><li style="list-style-type:disc">UL 1738 special vent rules for Cat II–IV</li></ul><p id="22c2881f-86f1-8059-ad91-db01a12de1c5" class=""><strong>6.2.3.2 Category Classification Algorithm</strong></p><ul id="22c2881f-86f1-804e-8ff8-c05404543a71" class="bulleted-list"><li style="list-style-type:disc">Flue-gas temp vs. dew-point → condensation risk</li></ul><ul id="22c2881f-86f1-8063-b63f-eefd8f8c9654" class="bulleted-list"><li style="list-style-type:disc">Vent static pressure sign → negative/positive draft</li></ul><ul id="22c2881f-86f1-8081-8ae5-f0af47d4cd3d" class="bulleted-list"><li style="list-style-type:disc">Map to Cat I–IV and vent type</li></ul><p id="22c2881f-86f1-807c-a60f-c9218cd804b6" class=""><strong>******* Sizing Methods</strong></p><ul id="22c2881f-86f1-80ae-bc33-e5a6df379a65" class="bulleted-list"><li style="list-style-type:disc">Prescriptive: Lookup NFPA 54 tables (Table 13.x)</li></ul><ul id="22c2881f-86f1-806d-af1e-c7c0b48883e7" class="bulleted-list"><li style="list-style-type:disc">Performance: Darcy–Weisbach + equivalent length/fitting loss</li></ul><p id="22c2881f-86f1-804f-a484-ebfeb2c1ca3b" class=""><strong>******* Draft &amp; Condensation Checks</strong></p><ul id="22c2881f-86f1-8034-bb94-f227701bc60a" class="bulleted-list"><li style="list-style-type:disc">Compute theoretical stack draft (stack effect)</li></ul><ul id="22c2881f-86f1-808c-8afe-f38bae7a87b3" class="bulleted-list"><li style="list-style-type:disc">Subtract friction + fittings → net draft vs. required</li></ul><ul id="22c2881f-86f1-80ac-b0f2-ca4d2568c77d" class="bulleted-list"><li style="list-style-type:disc">Dew-point check → condensation warning</li></ul><p id="22c2881f-86f1-80c5-9a40-db435b55f639" class=""><strong>******* Validation &amp; Pass/Fail Reports</strong></p><ul id="22c2881f-86f1-8035-b30f-eb84330ea0dd" class="bulleted-list"><li style="list-style-type:disc">Clause-by-clause pass/fail (with code references)</li></ul><ul id="22c2881f-86f1-8030-b8ff-e93b07529151" class="bulleted-list"><li style="list-style-type:disc">“Justification trace” for each decision</li></ul><p id="22c2881f-86f1-80aa-9a31-feda6096fe1b" class=""><strong>6.2.4 Saving &amp; Sharing Your Work</strong></p><p id="22c2881f-86f1-809e-a09d-dfa2bfad4fbc" class=""><strong>6.2.4.1 Export Formats</strong></p><ul id="22c2881f-86f1-80e0-a77c-f709ed45cf35" class="bulleted-list"><li style="list-style-type:disc">Computational Report: PDF, CSV, Excel (full inputs, steps, code cites)</li></ul><ul id="22c2881f-86f1-80ef-ba0d-f1ff70aee4b6" class="bulleted-list"><li style="list-style-type:disc">Graphics: SVG, DXF, DWG of your vent layout</li></ul><p id="22c2881f-86f1-805b-b46f-ee733dd4ce2b" class=""><strong>6.2.4.2 Data Storage &amp; Retrieval</strong></p><ul id="22c2881f-86f1-8050-95fc-d1a6c2bb6dbf" class="bulleted-list"><li style="list-style-type:disc">Offline-first local cache (IndexedDB)</li></ul><ul id="22c2881f-86f1-8092-b392-d85cced1579a" class="bulleted-list"><li style="list-style-type:disc">Optional secure cloud sync (project profiles &amp; reports)</li></ul><ul id="22c2881f-86f1-808b-b651-cdefb7a9789f" class="bulleted-list"><li style="list-style-type:disc">Versioned project archives</li></ul><p id="22c2881f-86f1-80b3-9692-d632986be155" class=""><strong>6.2.4.3 Collaborating &amp; Handoff</strong></p><ul id="22c2881f-86f1-8090-b0eb-c8f7e8874cf3" class="bulleted-list"><li style="list-style-type:disc">Share link or package with colleagues</li></ul><ul id="22c2881f-86f1-8008-ad62-d7c4866baa19" class="bulleted-list"><li style="list-style-type:disc">Direct feed into Estimating App or BIM plugin</li></ul><p id="22c2881f-86f1-808c-bdb2-d2e9a552c776" class=""><strong>6.2.5 Developer &amp; Maintenance Guide</strong></p><p id="22c2881f-86f1-809f-8312-d3c43d74426b" class=""><strong>6.2.5.1 Code Structure &amp; Modules</strong></p><ul id="22c2881f-86f1-8099-b399-c54703ad3427" class="bulleted-list"><li style="list-style-type:disc">Python calculation package (classify, size, validate)</li></ul><ul id="22c2881f-86f1-809c-88f6-f4ca5417bf59" class="bulleted-list"><li style="list-style-type:disc">JS UI layers &amp; canvas renderer</li></ul><p id="22c2881f-86f1-801f-941d-c1b1669a051a" class=""><strong>6.2.5.2 Testing &amp; CI/CD</strong></p><ul id="22c2881f-86f1-8095-8437-c8542c347946" class="bulleted-list"><li style="list-style-type:disc">Unit tests (100% coverage on core logic)</li></ul><ul id="22c2881f-86f1-8012-b2c2-d58fa9d9d6df" class="bulleted-list"><li style="list-style-type:disc">Doctests &amp; example notebooks for regression</li></ul><ul id="22c2881f-86f1-809b-a3b7-c25e9cf63a19" class="bulleted-list"><li style="list-style-type:disc">Automated doc-build (Sphinx + MkDocs)</li></ul><p id="22c2881f-86f1-8036-86a0-f82299d1dfd1" class=""><strong>6.2.5.3 Extensibility &amp; Roadmap</strong></p><ul id="22c2881f-86f1-8016-9dc9-f366593a929b" class="bulleted-list"><li style="list-style-type:disc">Planned Phase 2: multi-appliance common vent</li></ul><ul id="22c2881f-86f1-80c5-beef-d9ba6899e77b" class="bulleted-list"><li style="list-style-type:disc">Plugin hooks for new standards (CSA, EN)</li></ul><ul id="22c2881f-86f1-80c4-a07c-dea7f54274fc" class="bulleted-list"><li style="list-style-type:disc">UI enhancements (3D view, real-time collaboration)</li></ul><p id="22c2881f-86f1-8023-975d-fef9d048f6fd" class=""><strong>6.2.6 Use Cases &amp; Worked Examples</strong></p><p id="22c2881f-86f1-8065-9aaf-c7c3d61f35bc" class=""><strong>6.2.6.1 Residential Single-Boiler Example</strong></p><ul id="22c2881f-86f1-804b-beca-fba8a9b940d6" class="bulleted-list"><li style="list-style-type:disc">200 MBH natural gas, 30 ft rise, 3 elbows: full step-by-step</li></ul><p id="22c2881f-86f1-80c7-bfdf-d91d9fcf2e4d" class=""><strong>6.2.6.2 Commercial Common-Vent Scenario</strong></p><ul id="22c2881f-86f1-8015-b60c-c6569c70214c" class="bulleted-list"><li style="list-style-type:disc">Two 150 MBH boilers sharing a Type B vent</li></ul><p id="22c2881f-86f1-80aa-b355-fa9817273edd" class=""><strong>6.2.6.3 Challenging Conditions</strong></p><ul id="22c2881f-86f1-803a-903b-cd414a6bc783" class="bulleted-list"><li style="list-style-type:disc">High-elevation site (≥ 5 000 ft)</li></ul><ul id="22c2881f-86f1-805b-991f-d50c00d3edca" class="bulleted-list"><li style="list-style-type:disc">Condensing boiler with low flue temp</li></ul><p id="22c2881f-86f1-80e6-946b-d0ac2f026e3e" class=""><strong>6.2.7 Glossary &amp; References</strong></p><p id="22c2881f-86f1-8030-8650-d341ad71ae1b" class=""><strong>6.2.7.1 Key Terms</strong></p><ul id="22c2881f-86f1-8057-b0a2-cc68debbf075" class="bulleted-list"><li style="list-style-type:disc">MBH, in w.c., Category I–IV, equivalent length, etc.</li></ul><p id="22c2881f-86f1-80d6-be6d-f4c512a81cca" class=""><strong>6.2.7.2 Clause Cross-Walk</strong></p><table id="22c2881f-86f1-8054-a6df-cbdbe243200c" class="simple-table"><tbody><tr id="22c2881f-86f1-801a-9b38-fb096b04084c"><td id="fEn@" class=""><strong>Feature</strong></td><td id="P`RM" class=""><strong>Standard / Clause</strong></td></tr><tr id="22c2881f-86f1-80c1-8b40-ceb7c7327816"><td id="fEn@" class="">Cat I sizing table</td><td id="P`RM" class="">NFPA 54 §13.1 Table 13.1</td></tr><tr id="22c2881f-86f1-806d-b607-cedda12aec67"><td id="fEn@" class="">Draft hood requirements</td><td id="P`RM" class="">NFPA 54 §12.11</td></tr></tbody></table><p id="22c2881f-86f1-8057-8017-d4e5200df5ff" class=""><strong>6.2.7.3 Bibliography</strong></p><ul id="22c2881f-86f1-806e-8586-dd2ad51a408a" class="bulleted-list"><li style="list-style-type:disc">NFPA 54–2021, IFGC 2021, UL 1738, ASHRAE Fundamentals</li></ul><p id="22c2881f-86f1-80b9-aaaa-cfe88ee378ab" class=""><strong>6.2.8 Legal &amp; Compliance</strong></p><p id="22c2881f-86f1-80aa-a04a-e825ab8a14fb" class=""><strong>6.2.8.1 Regulatory Conformance</strong></p><ul id="22c2881f-86f1-803a-9af9-f8f5c4942ef0" class="bulleted-list"><li style="list-style-type:disc">Tool follows consensus standards; AHJ review required</li></ul><p id="22c2881f-86f1-80ab-ae34-d334c9e89573" class=""><strong>6.2.8.2 Liability &amp; Disclaimers</strong></p><ul id="22c2881f-86f1-8072-ba23-d58ed88a3ca9" class="bulleted-list"><li style="list-style-type:disc">Engineering judgment required; no warranty on third-party approvals</li></ul><p id="22c2881f-86f1-8020-ab1b-f9ecc3c0d749" class=""><strong>6.2.8.3 Data Privacy &amp; Security</strong></p><ul id="************************************" class="bulleted-list"><li style="list-style-type:disc">Local data only unless user opts into secure cloud sync</li></ul><ul id="22c2881f-86f1-8016-8677-dd55489f106b" class="bulleted-list"><li style="list-style-type:disc">GDPR-compliant for shared projects</li></ul><p id="22c2881f-86f1-806a-bbcd-df15d2afdced" class="">This eight-section outline answers your eight questions, remains concise, and groups related topics logically. Let me know if you want to tweak any heading before we start drafting the chapter text!</p><p id="22c2881f-86f1-8090-b776-d1c06d7c64af" class="">
</p><p id="22b2881f-86f1-807b-a10c-f1d9e2758ec8" class="">/mermaid</p><script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js" integrity="sha512-7Z9J3l1+EYfeaPKcGXu3MS/7T+w19WtKQY/n+xzmw4hZhJ9tyYmcUS+4QqAlzhicE5LAfMQSF3iFTK9bQdTxXg==" crossorigin="anonymous" referrerPolicy="no-referrer"></script><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" integrity="sha512-tN7Ec6zAFaVSG3TpNAKtk4DOHNpSwKHxxrsiw4GHKESGPs5njn/0sMCUMl2svV4wo4BK/rCP7juYz+zx+l6oeQ==" crossorigin="anonymous" referrerPolicy="no-referrer"/><pre id="22c2881f-86f1-809c-bdfd-cfffd3321609" class="code"><code class="language-Mermaid">flowchart TD
    Start --&gt; Classify
    Classify --&gt; NFPA_Rules

    NFPA_Rules --&gt; Rule1
    NFPA_Rules --&gt; Rule2
    NFPA_Rules --&gt; Rule3
    NFPA_Rules --&gt; Rule4

    Rule1 -- Pass --&gt; FinalOK
    Rule2 -- Pass --&gt; FinalOK
    Rule3 -- Pass --&gt; FinalOK
    Rule4 -- Pass --&gt; FinalOK

    Rule1 -- Fail --&gt; Fail1
    Rule2 -- Fail --&gt; Fail2
    Rule3 -- Fail --&gt; Fail3
    Rule4 -- Fail --&gt; Fail4</code></pre></div></article><span class="sans" style="font-size:14px;padding-top:2em"></span></body></html>