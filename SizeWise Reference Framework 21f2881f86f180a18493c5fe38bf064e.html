<html><head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"/><title>SizeWise Reference Framework</title><style>
/* cspell:disable-file */
/* webkit printing magic: print all background colors */
html {
	-webkit-print-color-adjust: exact;
}
* {
	box-sizing: border-box;
	-webkit-print-color-adjust: exact;
}

html,
body {
	margin: 0;
	padding: 0;
}
@media only screen {
	body {
		margin: 2em auto;
		max-width: 900px;
		color: rgb(55, 53, 47);
	}
}

body {
	line-height: 1.5;
	white-space: pre-wrap;
}

a,
a.visited {
	color: inherit;
	text-decoration: underline;
}

.pdf-relative-link-path {
	font-size: 80%;
	color: #444;
}

h1,
h2,
h3 {
	letter-spacing: -0.01em;
	line-height: 1.2;
	font-weight: 600;
	margin-bottom: 0;
}

.page-title {
	font-size: 2.5rem;
	font-weight: 700;
	margin-top: 0;
	margin-bottom: 0.75em;
}

h1 {
	font-size: 1.875rem;
	margin-top: 1.875rem;
}

h2 {
	font-size: 1.5rem;
	margin-top: 1.5rem;
}

h3 {
	font-size: 1.25rem;
	margin-top: 1.25rem;
}

.source {
	border: 1px solid #ddd;
	border-radius: 3px;
	padding: 1.5em;
	word-break: break-all;
}

.callout {
	border-radius: 3px;
	padding: 1rem;
}

figure {
	margin: 1.25em 0;
	page-break-inside: avoid;
}

figcaption {
	opacity: 0.5;
	font-size: 85%;
	margin-top: 0.5em;
}

mark {
	background-color: transparent;
}

.indented {
	padding-left: 1.5em;
}

hr {
	background: transparent;
	display: block;
	width: 100%;
	height: 1px;
	visibility: visible;
	border: none;
	border-bottom: 1px solid rgba(55, 53, 47, 0.09);
}

img {
	max-width: 100%;
}

@media only print {
	img {
		max-height: 100vh;
		object-fit: contain;
	}
}

@page {
	margin: 1in;
}

.collection-content {
	font-size: 0.875rem;
}

.column-list {
	display: flex;
	justify-content: space-between;
}

.column {
	padding: 0 1em;
}

.column:first-child {
	padding-left: 0;
}

.column:last-child {
	padding-right: 0;
}

.table_of_contents-item {
	display: block;
	font-size: 0.875rem;
	line-height: 1.3;
	padding: 0.125rem;
}

.table_of_contents-indent-1 {
	margin-left: 1.5rem;
}

.table_of_contents-indent-2 {
	margin-left: 3rem;
}

.table_of_contents-indent-3 {
	margin-left: 4.5rem;
}

.table_of_contents-link {
	text-decoration: none;
	opacity: 0.7;
	border-bottom: 1px solid rgba(55, 53, 47, 0.18);
}

table,
th,
td {
	border: 1px solid rgba(55, 53, 47, 0.09);
	border-collapse: collapse;
}

table {
	border-left: none;
	border-right: none;
}

th,
td {
	font-weight: normal;
	padding: 0.25em 0.5em;
	line-height: 1.5;
	min-height: 1.5em;
	text-align: left;
}

th {
	color: rgba(55, 53, 47, 0.6);
}

ol,
ul {
	margin: 0;
	margin-block-start: 0.6em;
	margin-block-end: 0.6em;
}

li > ol:first-child,
li > ul:first-child {
	margin-block-start: 0.6em;
}

ul > li {
	list-style: disc;
}

ul.to-do-list {
	padding-inline-start: 0;
}

ul.to-do-list > li {
	list-style: none;
}

.to-do-children-checked {
	text-decoration: line-through;
	opacity: 0.375;
}

ul.toggle > li {
	list-style: none;
}

ul {
	padding-inline-start: 1.7em;
}

ul > li {
	padding-left: 0.1em;
}

ol {
	padding-inline-start: 1.6em;
}

ol > li {
	padding-left: 0.2em;
}

.mono ol {
	padding-inline-start: 2em;
}

.mono ol > li {
	text-indent: -0.4em;
}

.toggle {
	padding-inline-start: 0em;
	list-style-type: none;
}

/* Indent toggle children */
.toggle > li > details {
	padding-left: 1.7em;
}

.toggle > li > details > summary {
	margin-left: -1.1em;
}

.selected-value {
	display: inline-block;
	padding: 0 0.5em;
	background: rgba(206, 205, 202, 0.5);
	border-radius: 3px;
	margin-right: 0.5em;
	margin-top: 0.3em;
	margin-bottom: 0.3em;
	white-space: nowrap;
}

.collection-title {
	display: inline-block;
	margin-right: 1em;
}

.page-description {
	margin-bottom: 2em;
}

.simple-table {
	margin-top: 1em;
	font-size: 0.875rem;
	empty-cells: show;
}
.simple-table td {
	height: 29px;
	min-width: 120px;
}

.simple-table th {
	height: 29px;
	min-width: 120px;
}

.simple-table-header-color {
	background: rgb(247, 246, 243);
	color: black;
}
.simple-table-header {
	font-weight: 500;
}

time {
	opacity: 0.5;
}

.icon {
	display: inline-block;
	max-width: 1.2em;
	max-height: 1.2em;
	text-decoration: none;
	vertical-align: text-bottom;
	margin-right: 0.5em;
}

img.icon {
	border-radius: 3px;
}

.user-icon {
	width: 1.5em;
	height: 1.5em;
	border-radius: 100%;
	margin-right: 0.5rem;
}

.user-icon-inner {
	font-size: 0.8em;
}

.text-icon {
	border: 1px solid #000;
	text-align: center;
}

.page-cover-image {
	display: block;
	object-fit: cover;
	width: 100%;
	max-height: 30vh;
}

.page-header-icon {
	font-size: 3rem;
	margin-bottom: 1rem;
}

.page-header-icon-with-cover {
	margin-top: -0.72em;
	margin-left: 0.07em;
}

.page-header-icon img {
	border-radius: 3px;
}

.link-to-page {
	margin: 1em 0;
	padding: 0;
	border: none;
	font-weight: 500;
}

p > .user {
	opacity: 0.5;
}

td > .user,
td > time {
	white-space: nowrap;
}

input[type="checkbox"] {
	transform: scale(1.5);
	margin-right: 0.6em;
	vertical-align: middle;
}

p {
	margin-top: 0.5em;
	margin-bottom: 0.5em;
}

.image {
	border: none;
	margin: 1.5em 0;
	padding: 0;
	border-radius: 0;
	text-align: center;
}

.code,
code {
	background: rgba(135, 131, 120, 0.15);
	border-radius: 3px;
	padding: 0.2em 0.4em;
	border-radius: 3px;
	font-size: 85%;
	tab-size: 2;
}

code {
	color: #eb5757;
}

.code {
	padding: 1.5em 1em;
}

.code-wrap {
	white-space: pre-wrap;
	word-break: break-all;
}

.code > code {
	background: none;
	padding: 0;
	font-size: 100%;
	color: inherit;
}

blockquote {
	font-size: 1.25em;
	margin: 1em 0;
	padding-left: 1em;
	border-left: 3px solid rgb(55, 53, 47);
}

.bookmark {
	text-decoration: none;
	max-height: 8em;
	padding: 0;
	display: flex;
	width: 100%;
	align-items: stretch;
}

.bookmark-title {
	font-size: 0.85em;
	overflow: hidden;
	text-overflow: ellipsis;
	height: 1.75em;
	white-space: nowrap;
}

.bookmark-text {
	display: flex;
	flex-direction: column;
}

.bookmark-info {
	flex: 4 1 180px;
	padding: 12px 14px 14px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.bookmark-image {
	width: 33%;
	flex: 1 1 180px;
	display: block;
	position: relative;
	object-fit: cover;
	border-radius: 1px;
}

.bookmark-description {
	color: rgba(55, 53, 47, 0.6);
	font-size: 0.75em;
	overflow: hidden;
	max-height: 4.5em;
	word-break: break-word;
}

.bookmark-href {
	font-size: 0.75em;
	margin-top: 0.25em;
}

.sans { font-family: ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol"; }
.code { font-family: "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace; }
.serif { font-family: Lyon-Text, Georgia, ui-serif, serif; }
.mono { font-family: iawriter-mono, Nitti, Menlo, Courier, monospace; }
.pdf .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK JP'; }
.pdf:lang(zh-CN) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK SC'; }
.pdf:lang(zh-TW) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK TC'; }
.pdf:lang(ko-KR) .sans { font-family: Inter, ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI Variable Display", "Segoe UI", Helvetica, "Apple Color Emoji", Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", 'Twemoji', 'Noto Color Emoji', 'Noto Sans CJK KR'; }
.pdf .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK JP'; }
.pdf:lang(zh-CN) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC'; }
.pdf:lang(zh-TW) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK TC'; }
.pdf:lang(ko-KR) .code { font-family: Source Code Pro, "SFMono-Regular", Menlo, Consolas, "PT Mono", "Liberation Mono", Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK KR'; }
.pdf .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK JP'; }
.pdf:lang(zh-CN) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK SC'; }
.pdf:lang(zh-TW) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK TC'; }
.pdf:lang(ko-KR) .serif { font-family: PT Serif, Lyon-Text, Georgia, ui-serif, serif, 'Twemoji', 'Noto Color Emoji', 'Noto Serif CJK KR'; }
.pdf .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK JP'; }
.pdf:lang(zh-CN) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK SC'; }
.pdf:lang(zh-TW) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK TC'; }
.pdf:lang(ko-KR) .mono { font-family: PT Mono, iawriter-mono, Nitti, Menlo, Courier, monospace, 'Twemoji', 'Noto Color Emoji', 'Noto Sans Mono CJK KR'; }
.highlight-default {
	color: rgba(50, 48, 44, 1);
}
.highlight-gray {
	color: rgba(115, 114, 110, 1);
	fill: rgba(115, 114, 110, 1);
}
.highlight-brown {
	color: rgba(159, 107, 83, 1);
	fill: rgba(159, 107, 83, 1);
}
.highlight-orange {
	color: rgba(217, 115, 13, 1);
	fill: rgba(217, 115, 13, 1);
}
.highlight-yellow {
	color: rgba(203, 145, 47, 1);
	fill: rgba(203, 145, 47, 1);
}
.highlight-teal {
	color: rgba(68, 131, 97, 1);
	fill: rgba(68, 131, 97, 1);
}
.highlight-blue {
	color: rgba(51, 126, 169, 1);
	fill: rgba(51, 126, 169, 1);
}
.highlight-purple {
	color: rgba(144, 101, 176, 1);
	fill: rgba(144, 101, 176, 1);
}
.highlight-pink {
	color: rgba(193, 76, 138, 1);
	fill: rgba(193, 76, 138, 1);
}
.highlight-red {
	color: rgba(205, 60, 58, 1);
	fill: rgba(205, 60, 58, 1);
}
.highlight-default_background {
	color: rgba(50, 48, 44, 1);
}
.highlight-gray_background {
	background: rgba(248, 248, 247, 1);
}
.highlight-brown_background {
	background: rgba(244, 238, 238, 1);
}
.highlight-orange_background {
	background: rgba(251, 236, 221, 1);
}
.highlight-yellow_background {
	background: rgba(251, 243, 219, 1);
}
.highlight-teal_background {
	background: rgba(237, 243, 236, 1);
}
.highlight-blue_background {
	background: rgba(231, 243, 248, 1);
}
.highlight-purple_background {
	background: rgba(248, 243, 252, 1);
}
.highlight-pink_background {
	background: rgba(252, 241, 246, 1);
}
.highlight-red_background {
	background: rgba(253, 235, 236, 1);
}
.block-color-default {
	color: inherit;
	fill: inherit;
}
.block-color-gray {
	color: rgba(115, 114, 110, 1);
	fill: rgba(115, 114, 110, 1);
}
.block-color-brown {
	color: rgba(159, 107, 83, 1);
	fill: rgba(159, 107, 83, 1);
}
.block-color-orange {
	color: rgba(217, 115, 13, 1);
	fill: rgba(217, 115, 13, 1);
}
.block-color-yellow {
	color: rgba(203, 145, 47, 1);
	fill: rgba(203, 145, 47, 1);
}
.block-color-teal {
	color: rgba(68, 131, 97, 1);
	fill: rgba(68, 131, 97, 1);
}
.block-color-blue {
	color: rgba(51, 126, 169, 1);
	fill: rgba(51, 126, 169, 1);
}
.block-color-purple {
	color: rgba(144, 101, 176, 1);
	fill: rgba(144, 101, 176, 1);
}
.block-color-pink {
	color: rgba(193, 76, 138, 1);
	fill: rgba(193, 76, 138, 1);
}
.block-color-red {
	color: rgba(205, 60, 58, 1);
	fill: rgba(205, 60, 58, 1);
}
.block-color-default_background {
	color: inherit;
	fill: inherit;
}
.block-color-gray_background {
	background: rgba(248, 248, 247, 1);
}
.block-color-brown_background {
	background: rgba(244, 238, 238, 1);
}
.block-color-orange_background {
	background: rgba(251, 236, 221, 1);
}
.block-color-yellow_background {
	background: rgba(251, 243, 219, 1);
}
.block-color-teal_background {
	background: rgba(237, 243, 236, 1);
}
.block-color-blue_background {
	background: rgba(231, 243, 248, 1);
}
.block-color-purple_background {
	background: rgba(248, 243, 252, 1);
}
.block-color-pink_background {
	background: rgba(252, 241, 246, 1);
}
.block-color-red_background {
	background: rgba(253, 235, 236, 1);
}
.select-value-color-default { background-color: rgba(84, 72, 49, 0.08); }
.select-value-color-gray { background-color: rgba(84, 72, 49, 0.15); }
.select-value-color-brown { background-color: rgba(210, 162, 141, 0.35); }
.select-value-color-orange { background-color: rgba(224, 124, 57, 0.27); }
.select-value-color-yellow { background-color: rgba(236, 191, 66, 0.39); }
.select-value-color-green { background-color: rgba(123, 183, 129, 0.27); }
.select-value-color-blue { background-color: rgba(93, 165, 206, 0.27); }
.select-value-color-purple { background-color: rgba(168, 129, 197, 0.27); }
.select-value-color-pink { background-color: rgba(225, 136, 179, 0.27); }
.select-value-color-red { background-color: rgba(244, 171, 159, 0.4); }

.checkbox {
	display: inline-flex;
	vertical-align: text-bottom;
	width: 16;
	height: 16;
	background-size: 16px;
	margin-left: 2px;
	margin-right: 5px;
}

.checkbox-on {
	background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20width%3D%2216%22%20height%3D%2216%22%20fill%3D%22%2358A9D7%22%2F%3E%0A%3Cpath%20d%3D%22M6.71429%2012.2852L14%204.9995L12.7143%203.71436L6.71429%209.71378L3.28571%206.2831L2%207.57092L6.71429%2012.2852Z%22%20fill%3D%22white%22%2F%3E%0A%3C%2Fsvg%3E");
}

.checkbox-off {
	background-image: url("data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Crect%20x%3D%220.75%22%20y%3D%220.75%22%20width%3D%2214.5%22%20height%3D%2214.5%22%20fill%3D%22white%22%20stroke%3D%22%2336352F%22%20stroke-width%3D%221.5%22%2F%3E%0A%3C%2Fsvg%3E");
}
	
</style></head><body><article id="21f2881f-86f1-80a1-8493-c5fe38bf064e" class="page sans"><header><div class="page-header-icon undefined"><span class="icon">📄</span></div><h1 class="page-title">SizeWise Reference Framework</h1><p class="page-description"></p></header><div class="page-body"><ul id="2212881f-86f1-80d1-9f80-c82ebe7f0c01" class="toggle"><li><details open=""><summary><strong>Introduction</strong></summary><ul id="2212881f-86f1-80d5-90b6-f0e912b86c36" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">1.1. About SizeWise Suite</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-80b6-beff-fdcbe67947c6" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">1.2. Purpose of This Document</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-80b8-9b66-eb76f39c776c" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">1.3. Scope and Audience</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-803e-b432-c1ef4f10cf0e" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">1.4. Document Structure</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-8024-88f0-ebbefcab3d92" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">1.5. Conventions, Terminology &amp; Units</span><div class="indented"></div></li></ul></details></li></ul><ul id="2212881f-86f1-804d-badf-ef652a2ae72d" class="toggle"><li><details open=""><summary><strong>Overview of SizeWise Suite</strong></summary><ul id="2212881f-86f1-80e6-8704-fdc4b8d29ca3" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">2.1. Product Architecture &amp; Deployment Options</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-803c-858c-d7834ed9a20a" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">2.2. Key Features &amp; Benefits</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-8089-82c5-c50684eefb38" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">2.3. Supported Standards &amp; Codes</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-80df-aa8e-c890ddd8b3eb" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">2.4. Roadmap &amp; Planned Modules</span><div class="indented"></div></li></ul></details></li></ul><ul id="2212881f-86f1-80e6-91bf-f5645372d220" class="toggle"><li><details open=""><summary><strong>Getting Started</strong></summary><ul id="2212881f-86f1-80cd-8268-e697b86be272" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">3.1. System Requirements (Hardware &amp; Software)</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-80b3-9f0b-d1ac796f68b1" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">3.2. Installation Procedures (On-Premise / Cloud-Hosted)</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-80e2-a02e-d1daadc6b4a2" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">3.3. Licensing Model &amp; Activation</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-8075-addd-d1fb73d349fd" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">3.4. Initial Configuration Wizard</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-802d-8485-d5a02a52a7fe" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">3.5. User Roles &amp; Permission Matrix</span><div class="indented"></div></li></ul></details></li></ul><ul id="2212881f-86f1-8099-ab21-f6bc7e0e1a4f" class="toggle"><li><details open=""><summary><strong>Planning &amp; Project Setup</strong></summary><ul id="2212881f-86f1-8074-af03-ccb0d6632019" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">4.1. Creating a New Project</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-805a-87a1-d437baa41d75" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">4.2. Project Templates &amp; Standards Library</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-8063-9005-c13e75a54ccf" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">4.3. Importing External Data (CAD, BIM, Spreadsheets)</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-80fd-b5f7-db5c267c3c55" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">4.4. Defining Design Criteria &amp; Code Packages</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-8050-ae38-f54cd3f5bf5c" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">4.5. Resource Allocation &amp; Schedule Integration</span><div class="indented"></div></li></ul></details></li></ul><ul id="2212881f-86f1-8041-a146-eff661b04578" class="toggle"><li><details open=""><summary><strong>Financial Management &amp; Cost Estimating</strong></summary><ul id="2212881f-86f1-80ea-924e-cbfaee9d2842" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">5.1. Cost Database Structure &amp; Currency Support</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-80cc-baa1-f2b5d29448a4" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">5.2. Unit Costs, Labor Rates &amp; Markups</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-80ea-b1c8-f7a0f1a0e518" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">5.3. Estimation Workflows per Module</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-8060-8a15-fadb1cc293c4" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">5.4. Budget Tracking, Change Orders &amp; Variance Reports</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-802c-a0cb-f3eca15b1029" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">5.5. Exporting Estimates to ERP/Accounting Systems</span><div class="indented"></div></li></ul></details></li></ul><ul id="2212881f-86f1-805d-9389-d1af2ba3f2d4" class="toggle"><li><details open=""><summary><strong>Module Reference</strong></summary><ul id="2212881f-86f1-8014-ac57-e4bd50f6e39e" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">6.1. Air Duct Sizer</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-8032-bfde-e67399f6143a" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">6.1.1. Overview &amp; Applications</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-80a4-bc2c-d8d3c85baf71" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">6.1.2. Input Parameters (Airflows, Velocities, Layout)</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-807f-a389-eb2750918f7a" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">6.1.3. Sizing Algorithms &amp; Friction Methods</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-8023-b6ff-f6786587e481" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">6.1.4. Outputs: Duct Schedules &amp; Pressure Loss Reports</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-80f6-80a7-c680b80bd694" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">6.1.5. Worked Example &amp; Best Practices</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-809d-9571-f521a117b8da" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">6.2. Boiler Vent Sizer</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-8015-96e1-da74e2f8803f" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">6.2.1. Overview &amp; Applications</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-80c6-89b5-dc213be7e695" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">6.2.2. Fuel Types &amp; Combustion Data</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-80f3-bf02-e5f2c1da44f0" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">6.2.3. Stack Height &amp; Diameter Calculations</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-8040-b3a1-d68bee765294" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">6.2.4. Draft &amp; Temperature Considerations</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-8073-995b-e194759a296d" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">6.2.5. Sample Calculation &amp; Verification</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-80d4-8951-e5c87d1e56de" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">6.3. Grease Duct Sizer</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-8007-8539-e67d86f6de6c" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">6.3.1. Overview &amp; NFPA Compliance</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-8039-a9a3-c6e61157db02" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">6.3.2. Hood Performance &amp; Capture Velocity</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-8041-8041-d39048a318d5" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">6.3.3. Duct Sizing Tables &amp; Code References</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-809c-bc30-eec8b77ad8a4" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">6.3.4. Fire Enclosure Options &amp; Clearances</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-80de-b860-e28014984eb7" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">6.3.5. Illustrative Example</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-8057-b8c0-ff378ec43244" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">6.4. Engine Exhaust Sizer</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-8072-801c-cf5a082fdee7" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">6.4.1. Overview &amp; Engine Types</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-80c2-97cd-e3aae1f71302" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">6.4.2. Emissions Data &amp; Plume Rise</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-80f2-a85b-f7ce759fba86" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">6.4.3. Duct &amp; Stack Sizing for Hot Gases</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-8032-9c64-dd78ee3b202b" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">6.4.4. Noise Attenuation &amp; Silencer Selection</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-80b4-ad41-ecf9c748ee1f" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">6.4.5. Worked Example</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-80b3-bb1d-dd162f32a910" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">6.5. Future Modules (e.g. Fire Protection Pipe Sizer)</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-80ef-905d-ded697659451" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">6.5.1. Anticipated Functionality</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-8084-984e-d8b780298c66" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">6.5.2. Integration Points &amp; Data Flows</span><div class="indented"></div></li></ul></details></li></ul><ul id="2212881f-86f1-802d-9a7c-e8460cf4d1ff" class="toggle"><li><details open=""><summary><strong>User Interface &amp; Workflows</strong></summary><ul id="2212881f-86f1-8044-8b1e-ca83dede5533" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">7.1. Dashboard Overview &amp; Navigation</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-8094-b6a3-d6384d0246a8" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">7.2. Data Entry Forms &amp; Field Validation</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-8011-bd9d-dce03565b71d" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">7.3. Report Generation &amp; Custom Templates</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-8001-bd54-eedf1b7af237" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">7.4. Batch Processing &amp; Multi-Module Scenarios</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-804c-9370-dda86423d375" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">7.5. Alerts, Notifications &amp; Approval Workflows</span><div class="indented"></div></li></ul></details></li></ul><ul id="2212881f-86f1-80ed-bea7-c13473e4f581" class="toggle"><li><details open=""><summary><strong>Administration &amp; Configuration</strong></summary><ul id="2212881f-86f1-8010-9cb2-eba1675ca8a6" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">8.1. User &amp; Group Management</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-809a-a62e-e29570f2f6aa" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">8.2. Permission Profiles &amp; Access Control</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-8077-b5f1-e68ef0ec1512" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">8.3. System Settings, Defaults &amp; Global Parameters</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-80cc-ac28-dbf95cafce31" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">8.4. External Integrations (CAD/BIM, ERP, Document Repos)</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-8073-86b6-c23eb2500c45" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">8.5. Backup, Archiving &amp; Disaster Recovery</span><div class="indented"></div></li></ul></details></li></ul><ul id="2212881f-86f1-8061-ba01-d88bbe9ffcf7" class="toggle"><li><details open=""><summary><strong>Quality Assurance &amp; Validation</strong></summary><ul id="2212881f-86f1-80b3-8c5e-d8e862c30944" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">9.1. Verification &amp; Validation Procedures</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-80bb-b2fa-ea9387c78465" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">9.2. Cross-Check Tools &amp; Peer Review Templates</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-80fa-945d-f0ec9235a544" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">9.3. Audit Logs &amp; Change History</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-80b6-892c-dd7ff7e4b705" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">9.4. Traceability Matrices (Input ↔ Output)</span><div class="indented"></div></li></ul></details></li></ul><ul id="2212881f-86f1-8006-aee7-e172c64bee28" class="toggle"><li><details open=""><summary><strong>Training &amp; Onboarding</strong></summary><ul id="2212881f-86f1-8015-8d3a-e99f199da9e7" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">10.1. Recommended Learning Paths</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-80e8-9dce-ef2546b746fc" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">10.2. User Guides &amp; Video Tutorials</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-803d-827f-eab8feb79aa7" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">10.3. Certified Power-User Program</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-8030-b810-fb21f8dffdde" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">10.4. Knowledge-Base &amp; FAQs</span><div class="indented"></div></li></ul></details></li></ul><ul id="2212881f-86f1-809f-89e3-caac24939b5c" class="toggle"><li><details open=""><summary><strong>Troubleshooting &amp; Support</strong></summary><ul id="2212881f-86f1-8081-a49b-daa4ebbf21e7" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">11.1. Common Error Messages &amp; Resolutions</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-80db-ab77-c39b472c94da" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">11.2. Diagnostic Logs &amp; How to Capture Them</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-8029-a03d-ce8c592ae328" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">11.3. Escalation Procedure &amp; SLAs</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-8030-ac96-dc65b6143a48" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">11.4. Contacting Technical Support</span><div class="indented"></div></li></ul></details></li></ul><ul id="2212881f-86f1-800e-9541-e99fc1c36403" class="toggle"><li><details open=""><summary><strong>Security &amp; Compliance</strong></summary><ul id="2212881f-86f1-805f-b792-f653c85bca11" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">12.1. Data Encryption &amp; Privacy Controls</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-80ca-9ff3-e156bbf94ade" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">12.2. Authentication Methods &amp; Single Sign-On</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-801b-b4cc-ce402f9ee259" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">12.3. Regulatory Compliance (GDPR, local codes)</span><div class="indented"></div></li></ul><ul id="2212881f-86f1-8088-a97d-e168112029aa" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">12.4. Pen-testing &amp; Vulnerability Management</span><div class="indented"></div></li></ul></details></li></ul><ul id="2212881f-86f1-8035-88be-d7f8ea4660c0" class="toggle"><li><details open=""><summary><strong>Appendices</strong></summary><ul id="2212881f-86f1-80a2-afc3-fc6381c5ebf7" class="to-do-list"><li><div class="checkbox checkbox-off"></div> <span class="to-do-children-unchecked">A. Calculation Methodologies &amp; Formulas</span><div class="indented"></div></li></ul><p id="2212881f-86f1-80bc-aa05-d74f80b8cc1f" class="">B. Industry Standards &amp; Code References</p><p id="2212881f-86f1-808e-9c0b-e21287df2b65" class="">C. Glossary of Terms &amp; Acronyms</p><p id="2212881f-86f1-8052-a0ed-c50d3ee92502" class="">D. Release Notes &amp; Version History</p><p id="2212881f-86f1-80a6-8cb8-e3db041bb9b5" class="">E. Sample Project Files &amp; Templates</p></details></li></ul><ul id="2212881f-86f1-807c-96b6-c34c74d3aca1" class="toggle"><li><details open=""><summary><strong>Index</strong></summary><p id="2212881f-86f1-8095-bc1e-f745be112a17" class="">
</p></details></li></ul><p id="2212881f-86f1-8065-8c82-d94da7223bfb" class="">
</p><h1 id="2212881f-86f1-80f6-8ddc-e9cc4fbb7154" class="">SizeWise Suite – Boiler Vent Sizer Documentation Outline (Complete Indented Format with Details)</h1><figure id="2212881f-86f1-8083-9e2a-d9a47f3dda48" class="link-to-page"><a href="SizeWise%20Reference%20Framework%2021f2881f86f180a18493c5fe38bf064e/1%20Introduction%202212881f86f180839e2ad9a47f3dda48.html">1. Introduction</a></figure><figure id="2212881f-86f1-804d-8a64-f0d2edf4e919" class="link-to-page"><a href="SizeWise%20Reference%20Framework%2021f2881f86f180a18493c5fe38bf064e/2%20Overview%20of%20SizeWise%20Suite%202212881f86f1804d8a64f0d2edf4e919.html">2. Overview of SizeWise Suite</a></figure><figure id="2212881f-86f1-80c2-b6bb-c86861441c04" class="link-to-page"><a href="SizeWise%20Reference%20Framework%2021f2881f86f180a18493c5fe38bf064e/3%20Getting%20Started%202212881f86f180c2b6bbc86861441c04.html">3. Getting Started</a></figure><figure id="2212881f-86f1-8072-abd3-e1415a6d491b" class="link-to-page"><a href="SizeWise%20Reference%20Framework%2021f2881f86f180a18493c5fe38bf064e/4%20Planning%20&amp;%20Project%20Setup%202212881f86f18072abd3e1415a6d491b.html">4. Planning &amp; Project Setup</a></figure><figure id="2212881f-86f1-8092-8c45-ffc792b3bdb6" class="link-to-page"><a href="SizeWise%20Reference%20Framework%2021f2881f86f180a18493c5fe38bf064e/5%20Financial%20Management%20&amp;%20Cost%20Estimating%202212881f86f180928c45ffc792b3bdb6.html">5. Financial Management &amp; Cost Estimating</a></figure><figure id="2212881f-86f1-8011-a563-e71361fdd99c" class="link-to-page"><a href="SizeWise%20Reference%20Framework%2021f2881f86f180a18493c5fe38bf064e/6%20Module%20Reference%202212881f86f18011a563e71361fdd99c.html">6. Module Reference</a></figure><figure id="2212881f-86f1-803d-b441-cf28a97d4c49" class="link-to-page"><a href="SizeWise%20Reference%20Framework%2021f2881f86f180a18493c5fe38bf064e/7%20User%20Interface%20&amp;%20Workflows%202212881f86f1803db441cf28a97d4c49.html">7. User Interface &amp; Workflows</a></figure><figure id="2212881f-86f1-8023-90d6-f7d30bc73479" class="link-to-page"><a href="SizeWise%20Reference%20Framework%2021f2881f86f180a18493c5fe38bf064e/8%20Administration%20&amp;%20Configuration%202212881f86f1802390d6f7d30bc73479.html">8. Administration &amp; Configuration</a></figure><figure id="2212881f-86f1-8022-827f-d1f8adb64ddb" class="link-to-page"><a href="SizeWise%20Reference%20Framework%2021f2881f86f180a18493c5fe38bf064e/9%20Quality%20Assurance%20&amp;%20Validation%202212881f86f18022827fd1f8adb64ddb.html">9. Quality Assurance &amp; Validation</a></figure><figure id="2212881f-86f1-80dd-a586-dbfedf82a4dc" class="link-to-page"><a href="SizeWise%20Reference%20Framework%2021f2881f86f180a18493c5fe38bf064e/10%20Training%20&amp;%20Onboarding%202212881f86f180dda586dbfedf82a4dc.html">10. Training &amp; Onboarding</a></figure><figure id="2212881f-86f1-8050-8e67-fce16fad1ba9" class="link-to-page"><a href="SizeWise%20Reference%20Framework%2021f2881f86f180a18493c5fe38bf064e/11%20Troubleshooting%20&amp;%20Support%202212881f86f180508e67fce16fad1ba9.html">11. Troubleshooting &amp; Support</a></figure><figure id="2212881f-86f1-80be-a069-cf9378019c15" class="link-to-page"><a href="SizeWise%20Reference%20Framework%2021f2881f86f180a18493c5fe38bf064e/12%20Security%20&amp;%20Compliance%202212881f86f180bea069cf9378019c15.html">12. Security &amp; Compliance</a></figure><figure id="2212881f-86f1-8038-aad5-d4d9ac1a1dc2" class="link-to-page"><a href="SizeWise%20Reference%20Framework%2021f2881f86f180a18493c5fe38bf064e/13%20Appendices%202212881f86f18038aad5d4d9ac1a1dc2.html">13. Appendices</a></figure><hr id="2212881f-86f1-802d-85ea-d12f8a62f4bb"/><p id="2212881f-86f1-8008-80c7-edc10d149665" class=""><strong>Note:</strong> Each section will eventually be expanded with detailed narrative, diagrams, sample JSON files, UI screenshots, formula derivations, and validation results based on testing and SME feedback.</p><p id="2222881f-86f1-80c4-b2d1-f38f8e87c3e6" class="">
</p><figure id="21d2881f-86f1-8098-9799-f9e30919ec6f" class="link-to-page"><a href="SizeWise%20Reference%20Framework%2021f2881f86f180a18493c5fe38bf064e/Boiler%20Vent%20Sizer%2021d2881f86f180989799f9e30919ec6f.html"><span class="icon">📄</span>Boiler Vent Sizer</a></figure><figure id="21f2881f-86f1-80e3-818a-f9ee01766c46" class="link-to-page"><a href="SizeWise%20Reference%20Framework%2021f2881f86f180a18493c5fe38bf064e/Boiler%20Vent%20Sizer%20V%201%203%2021f2881f86f180e3818af9ee01766c46.html"><span class="icon">📄</span>Boiler Vent Sizer V 1.3</a></figure><figure id="2252881f-86f1-811a-a886-d8dacd3ff4a9" class="link-to-page"><a href="SizeWise%20Reference%20Framework%2021f2881f86f180a18493c5fe38bf064e/New%20n8n%20boiler%202252881f86f1811aa886d8dacd3ff4a9.html">New n8n boiler</a></figure><h2 id="2262881f-86f1-80cb-a55b-fe93d9a561b7" class=""><strong>1.4 Document Structure</strong></h2><p id="2262881f-86f1-80a6-80e8-dd6ce73134fe" class="">This section presents a structured overview of the SizeWise Suite documentation, tailored for technical users such as engineering majors, QA analysts, and software product developers. It is designed to help users navigate the documentation logically and to access content relevant to system architecture, estimation logic, compliance enforcement, and tool behavior. The document mirrors the development, validation, and implementation lifecycle of the SizeWise Suite and emphasizes clarity, modularity, and traceability.</p><p id="2262881f-86f1-806f-a090-d85f0d0fa366" class="">Each major section of this document corresponds to specific phases of the product’s lifecycle or team responsibilities. Whether the user is managing deployments, developing new modules, validating regulatory compliance, or writing support guides, the structure ensures that all reference materials are logically grouped and consistently presented.</p><p id="2262881f-86f1-80cf-bd0f-de0ddb54bc4c" class="">Where applicable, hyperlinks and internal bookmarks are embedded throughout this document to improve cross-referencing across related content (e.g., connecting estimation workflows to export logic or validation coverage to compliance rules). As more modules are developed, this document will evolve while maintaining strict versioning via the release notes and changelog. Section 8.6 outlines the documentation change control workflow.</p><h3 id="2262881f-86f1-8064-b566-c124417b6f66" class=""><strong>SizeWise Home and Interaction</strong></h3><p id="2262881f-86f1-806d-8a37-d4524866a8f3" class="">Introduces foundational knowledge for users new to the platform. These entries establish context, define key terminology, and orient users to how the document is structured and why it exists.</p><ul id="2262881f-86f1-803e-8d7b-f13b4141eaae" class="bulleted-list"><li style="list-style-type:disc"><strong>1. Introduction</strong><ul id="2262881f-86f1-80a2-83b2-c4646843e173" class="bulleted-list"><li style="list-style-type:circle">1.1 About SizeWise Suite</li></ul><ul id="2262881f-86f1-801c-94c0-ffe17b7654b4" class="bulleted-list"><li style="list-style-type:circle">1.2 Purpose of This Document</li></ul><ul id="2262881f-86f1-8070-8e13-d43b40eab8bc" class="bulleted-list"><li style="list-style-type:circle">1.3 Scope and Audience</li></ul><ul id="2262881f-86f1-80be-91e8-fd14a8f7aae5" class="bulleted-list"><li style="list-style-type:circle">1.4 Document Structure</li></ul><ul id="2262881f-86f1-809d-854a-e942a228d3e6" class="bulleted-list"><li style="list-style-type:circle">1.5 Conventions, Terminology &amp; Units</li></ul></li></ul><h3 id="2262881f-86f1-80ca-bb53-e7bb671f34d8" class=""><strong>Overview of SizeWise Suite</strong></h3><p id="2262881f-86f1-800b-bf1f-d281f76cc96e" class="">Covers core platform design, capabilities, supported standards, and the future direction of the suite’s roadmap.</p><ul id="2262881f-86f1-80b5-abd8-ca821e6ded91" class="bulleted-list"><li style="list-style-type:disc"><strong>2.1 Product Architecture &amp; Deployment Options</strong></li></ul><ul id="2262881f-86f1-803e-a75e-c48d26651619" class="bulleted-list"><li style="list-style-type:disc"><strong>2.2 Key Features &amp; Benefits</strong></li></ul><ul id="2262881f-86f1-80cb-8e7b-e44302cb41a9" class="bulleted-list"><li style="list-style-type:disc"><strong>2.3 Supported Standards &amp; Codes</strong></li></ul><ul id="2262881f-86f1-80b0-87a0-e000efbf5734" class="bulleted-list"><li style="list-style-type:disc"><strong>2.4 Roadmap &amp; Planned Modules</strong></li></ul><h3 id="2262881f-86f1-8062-baf5-d1af64d331e7" class=""><strong>Getting Started</strong></h3><p id="2262881f-86f1-8031-bd18-cf0c33087b46" class="">Outlines requirements and initial setup for system installation and user provisioning.</p><ul id="2262881f-86f1-806f-8ed6-d73ad31c7293" class="bulleted-list"><li style="list-style-type:disc"><strong>3.1 System Requirements (Hardware &amp; Software)</strong></li></ul><ul id="2262881f-86f1-80d5-a45b-d38a2d9a9666" class="bulleted-list"><li style="list-style-type:disc"><strong>3.2 Installation Procedures (On-Premise / Cloud-Hosted)</strong></li></ul><ul id="2262881f-86f1-8054-a002-d1747d68651e" class="bulleted-list"><li style="list-style-type:disc"><strong>3.3 Licensing Model &amp; Activation</strong></li></ul><ul id="2262881f-86f1-805b-9544-e3f06367a94e" class="bulleted-list"><li style="list-style-type:disc"><strong>3.4 Initial Configuration Wizard</strong></li></ul><ul id="2262881f-86f1-8040-8750-ee3ba489e8df" class="bulleted-list"><li style="list-style-type:disc"><strong>3.5 User Roles &amp; Permission Matrix</strong></li></ul><h3 id="2262881f-86f1-8067-b40f-d13de8889d0e" class=""><strong>Planning &amp; Project Setup</strong></h3><p id="2262881f-86f1-805a-91e2-c5c06725479e" class="">Describes project creation workflows, data integration from design tools, and scope definition with standards.</p><ul id="2262881f-86f1-8054-8d74-c8f9c47c2484" class="bulleted-list"><li style="list-style-type:disc"><strong>4.1 Creating a New Project</strong></li></ul><ul id="2262881f-86f1-801d-88d8-dda4bd80d678" class="bulleted-list"><li style="list-style-type:disc"><strong>4.2 Project Templates &amp; Standards Library</strong></li></ul><ul id="2262881f-86f1-8036-98c2-d67930ffc6d1" class="bulleted-list"><li style="list-style-type:disc"><strong>4.3 Importing External Data (CAD, BIM, Spreadsheets)</strong></li></ul><ul id="2262881f-86f1-800b-881c-e9c23e6df0e8" class="bulleted-list"><li style="list-style-type:disc"><strong>4.4 Defining Design Criteria &amp; Code Packages</strong></li></ul><ul id="2262881f-86f1-80a7-bafe-d3b9b9185f3f" class="bulleted-list"><li style="list-style-type:disc"><strong>4.5 Resource Allocation &amp; Schedule Integration</strong></li></ul><h3 id="2262881f-86f1-80d0-b2a5-db606f706776" class=""><strong>Financial Management &amp; Cost Estimating</strong></h3><p id="2262881f-86f1-80ff-ac7e-f5d188824c56" class="">Explains data structuring for pricing and estimation, including integration with financial platforms.</p><ul id="2262881f-86f1-8072-bb83-dfcf8e46afb2" class="bulleted-list"><li style="list-style-type:disc"><strong>5.1 Cost Database Structure &amp; Currency Support</strong></li></ul><ul id="2262881f-86f1-8065-936a-f11b07854bf9" class="bulleted-list"><li style="list-style-type:disc"><strong>5.2 Unit Costs, Labor Rates &amp; Markups</strong></li></ul><ul id="2262881f-86f1-807b-897d-d970a31bd900" class="bulleted-list"><li style="list-style-type:disc"><strong>5.3 Estimation Workflows per Module</strong></li></ul><ul id="2262881f-86f1-808d-a048-cc83b56a7988" class="bulleted-list"><li style="list-style-type:disc"><strong>5.4 Budget Tracking, Change Orders &amp; Variance Reports</strong></li></ul><ul id="2262881f-86f1-8011-ac52-e037e44c0601" class="bulleted-list"><li style="list-style-type:disc"><strong>5.5 Exporting Estimates to ERP/Accounting Systems</strong></li></ul><h3 id="2262881f-86f1-8087-b7bd-e03678d1f9ea" class=""><strong>Module Reference</strong></h3><p id="2262881f-86f1-803e-908d-dbef8fcf6996" class="">Each tool within the SizeWise Suite—such as duct sizing or combustion venting—is covered in detail with technical specifications and usage patterns.</p><ul id="2262881f-86f1-80f0-8720-e1de67104fda" class="bulleted-list"><li style="list-style-type:disc"><strong>6.1 Air Duct Sizer</strong><ul id="2262881f-86f1-8071-879f-e08ec53520b2" class="bulleted-list"><li style="list-style-type:circle">6.1.1 Overview &amp; Applications</li></ul><ul id="2262881f-86f1-8025-a5eb-d9723f93d79d" class="bulleted-list"><li style="list-style-type:circle">6.1.2 Input Parameters</li></ul><ul id="2262881f-86f1-8057-9ae9-e436190a708d" class="bulleted-list"><li style="list-style-type:circle">6.1.3 Sizing Algorithms &amp; Friction Methods</li></ul><ul id="2262881f-86f1-800d-abd2-e9408fbc8f93" class="bulleted-list"><li style="list-style-type:circle">6.1.4 Outputs: Duct Schedules &amp; Reports</li></ul><ul id="2262881f-86f1-80d0-af5b-c72f37f05362" class="bulleted-list"><li style="list-style-type:circle">6.1.5 Worked Example &amp; Best Practices</li></ul></li></ul><ul id="2262881f-86f1-80a2-82a8-f7d4f7736d6d" class="bulleted-list"><li style="list-style-type:disc"><strong>6.2 Boiler Vent Sizer</strong><ul id="2262881f-86f1-804d-9acd-f3a5aa3a0e02" class="bulleted-list"><li style="list-style-type:circle">6.2.1 Overview &amp; Applications</li></ul><ul id="2262881f-86f1-8016-a8bb-f20b12ec3e4b" class="bulleted-list"><li style="list-style-type:circle">6.2.2 Fuel Types &amp; Combustion Data</li></ul><ul id="2262881f-86f1-8057-9086-eb779a933598" class="bulleted-list"><li style="list-style-type:circle">6.2.3 Stack Height &amp; Diameter Calculations</li></ul><ul id="2262881f-86f1-805c-abc2-c66f2862b6be" class="bulleted-list"><li style="list-style-type:circle">6.2.4 Draft &amp; Temperature Considerations</li></ul><ul id="2262881f-86f1-8069-84f6-fb3acb74a91a" class="bulleted-list"><li style="list-style-type:circle">6.2.5 Sample Calculation &amp; Verification</li></ul></li></ul><ul id="2262881f-86f1-8090-9dd3-e79830ed8664" class="bulleted-list"><li style="list-style-type:disc"><strong>6.3 Grease Duct Sizer</strong><ul id="2262881f-86f1-8095-8ee2-dfe0c4309e31" class="bulleted-list"><li style="list-style-type:circle">6.3.1 Overview &amp; NFPA Compliance</li></ul><ul id="2262881f-86f1-80ea-8da0-ee5728fbfa9d" class="bulleted-list"><li style="list-style-type:circle">6.3.2 Hood Performance &amp; Capture Velocity</li></ul><ul id="2262881f-86f1-8056-8f26-f2a3b644a055" class="bulleted-list"><li style="list-style-type:circle">6.3.3 Duct Sizing Tables &amp; Code References</li></ul><ul id="2262881f-86f1-805f-b5ce-eb59c8bae9eb" class="bulleted-list"><li style="list-style-type:circle">6.3.4 Fire Enclosure Options &amp; Clearances</li></ul><ul id="2262881f-86f1-801c-a8d0-cc8912f60d7f" class="bulleted-list"><li style="list-style-type:circle">6.3.5 Illustrative Example</li></ul></li></ul><ul id="2262881f-86f1-8048-9bd1-d9feca083e3c" class="bulleted-list"><li style="list-style-type:disc"><strong>6.4 Engine Exhaust Sizer</strong><ul id="2262881f-86f1-8046-91a6-c5d94316dfbc" class="bulleted-list"><li style="list-style-type:circle">6.4.1 Overview &amp; Engine Types</li></ul><ul id="2262881f-86f1-8048-823a-fa54d19055f5" class="bulleted-list"><li style="list-style-type:circle">6.4.2 Emissions Data &amp; Plume Rise</li></ul><ul id="2262881f-86f1-806d-9e4d-c8893fb94d1f" class="bulleted-list"><li style="list-style-type:circle">6.4.3 Duct &amp; Stack Sizing for Hot Gases</li></ul><ul id="2262881f-86f1-809c-b90b-d48bdbc7c989" class="bulleted-list"><li style="list-style-type:circle">6.4.4 Noise Attenuation &amp; Silencer Selection</li></ul><ul id="2262881f-86f1-80c8-b681-d6293b9e5c15" class="bulleted-list"><li style="list-style-type:circle">6.4.5 Worked Example</li></ul></li></ul><ul id="2262881f-86f1-8015-b23e-d19252458acc" class="bulleted-list"><li style="list-style-type:disc"><strong>6.5 Future Modules</strong> (See Section 2.4 Roadmap for development sequence)<ul id="2262881f-86f1-8013-93db-ea36cfa2b5a1" class="bulleted-list"><li style="list-style-type:circle">6.5.1 Anticipated Functionality</li></ul><ul id="2262881f-86f1-804e-8dc8-e7af04c72838" class="bulleted-list"><li style="list-style-type:circle">6.5.2 Integration Points &amp; Data Flows</li></ul></li></ul><h3 id="2262881f-86f1-80dd-89a1-d281b29aff18" class=""><strong>User Interface &amp; Workflows</strong></h3><p id="2262881f-86f1-801e-bbc5-e8a72a9fa652" class="">Outlines user-facing interactions, workflow automation, validation logic, and UI consistency across the suite. Accessibility guidance for WCAG conformance is included.</p><ul id="2262881f-86f1-8010-87f8-e364ec33a1ca" class="bulleted-list"><li style="list-style-type:disc"><strong>7.1 Dashboard Overview &amp; Navigation</strong></li></ul><ul id="2262881f-86f1-8049-b0b5-f9f3561473b2" class="bulleted-list"><li style="list-style-type:disc"><strong>7.2 Data Entry Forms &amp; Field Validation</strong></li></ul><ul id="2262881f-86f1-8063-ad19-da879b4810da" class="bulleted-list"><li style="list-style-type:disc"><strong>7.3 Report Generation &amp; Custom Templates</strong></li></ul><ul id="2262881f-86f1-80c5-bb3e-e69a84c8ac25" class="bulleted-list"><li style="list-style-type:disc"><strong>7.4 Batch Processing &amp; Multi-Module Scenarios</strong></li></ul><ul id="2262881f-86f1-8024-a87f-d2bcf3966eba" class="bulleted-list"><li style="list-style-type:disc"><strong>7.5 Alerts, Notifications &amp; Approval Workflows</strong></li></ul><ul id="2262881f-86f1-8014-85c7-cfad45ec08f5" class="bulleted-list"><li style="list-style-type:disc"><strong>7.6 Accessibility &amp; WCAG Conformance</strong></li></ul><h3 id="2262881f-86f1-8057-ab6d-de6995d45dbd" class=""><strong>Administration &amp; Configuration</strong></h3><p id="2262881f-86f1-8044-a429-faa8f3126d88" class="">Defines permissions, system-wide configuration controls, integrations, and disaster recovery options.</p><ul id="2262881f-86f1-80d8-ab2d-d1fa4cd4bc2b" class="bulleted-list"><li style="list-style-type:disc"><strong>8.1 User &amp; Group Management</strong></li></ul><ul id="2262881f-86f1-80a4-bfb3-ecf10fc67446" class="bulleted-list"><li style="list-style-type:disc"><strong>8.2 Permission Profiles &amp; Access Control</strong></li></ul><ul id="2262881f-86f1-8090-99fd-f01a1a391a42" class="bulleted-list"><li style="list-style-type:disc"><strong>8.3 System Settings, Defaults &amp; Global Parameters</strong></li></ul><ul id="2262881f-86f1-8070-9b0f-ff6c725484ed" class="bulleted-list"><li style="list-style-type:disc"><strong>8.4 External Integrations (CAD/BIM, ERP, Document Repos)</strong></li></ul><ul id="2262881f-86f1-802f-ab00-e4787a551a2b" class="bulleted-list"><li style="list-style-type:disc"><strong>8.5 Backup, Archiving &amp; Disaster Recovery</strong></li></ul><ul id="2262881f-86f1-806c-8e61-dc8192be55ef" class="bulleted-list"><li style="list-style-type:disc"><strong>8.6 Documentation Change Control Workflow</strong></li></ul><h3 id="2262881f-86f1-80bc-961f-de5589bae7cd" class=""><strong>Quality Assurance &amp; Validation</strong></h3><p id="2262881f-86f1-8018-9679-e0295f6f18ad" class="">Covers the verification strategy, peer review processes, traceability mechanisms, and audit log practices.</p><ul id="2262881f-86f1-80f3-aa9b-e8eed3a3c123" class="bulleted-list"><li style="list-style-type:disc"><strong>9.1 Verification &amp; Validation Procedures</strong></li></ul><ul id="2262881f-86f1-80a6-b0a4-e7fb4836d2da" class="bulleted-list"><li style="list-style-type:disc"><strong>9.2 Cross-Check Tools &amp; Peer Review Templates</strong></li></ul><ul id="2262881f-86f1-80a6-bd20-eb13082b9905" class="bulleted-list"><li style="list-style-type:disc"><strong>9.3 Audit Logs &amp; Change History</strong></li></ul><ul id="2262881f-86f1-800b-a12f-dc5b21a234a5" class="bulleted-list"><li style="list-style-type:disc"><strong>9.4 Traceability Matrices (Input ↔ Output)</strong></li></ul><h3 id="2262881f-86f1-8060-9b85-fc6d7f4bc50e" class=""><strong>Training &amp; Onboarding</strong></h3><p id="2262881f-86f1-8078-a12a-e940664399f7" class="">Helps users build proficiency in the tools and documents through structured learning paths.</p><ul id="2262881f-86f1-80ad-bfb3-ca22f1d8f478" class="bulleted-list"><li style="list-style-type:disc"><strong>10.1 Recommended Learning Paths</strong></li></ul><ul id="2262881f-86f1-808f-bf01-d2570614810c" class="bulleted-list"><li style="list-style-type:disc"><strong>10.2 User Guides &amp; Video Tutorials</strong></li></ul><ul id="2262881f-86f1-80f0-a06b-c81fe91ea3d6" class="bulleted-list"><li style="list-style-type:disc"><strong>10.3 Certified Power-User Program</strong></li></ul><ul id="2262881f-86f1-80fe-8471-c34a9f728cc7" class="bulleted-list"><li style="list-style-type:disc"><strong>10.4 Knowledge-Base &amp; FAQs</strong></li></ul><h3 id="2262881f-86f1-8045-9f96-c7180735dd69" class=""><strong>Troubleshooting &amp; Support</strong></h3><p id="2262881f-86f1-805f-aafa-c5249e03ff36" class="">Provides escalation paths, diagnostic procedures, and points of contact for technical assistance.</p><ul id="2262881f-86f1-80ec-9f3e-c58933f5bb4f" class="bulleted-list"><li style="list-style-type:disc"><strong>11.1 Common Error Messages &amp; Resolutions</strong></li></ul><ul id="2262881f-86f1-80bb-be8e-ef91e2ecfebc" class="bulleted-list"><li style="list-style-type:disc"><strong>11.2 Diagnostic Logs &amp; How to Capture Them</strong></li></ul><ul id="2262881f-86f1-80a4-b91c-f2cd6e1f0ff9" class="bulleted-list"><li style="list-style-type:disc"><strong>11.3 Escalation Procedure &amp; SLAs</strong></li></ul><ul id="2262881f-86f1-8012-a02a-e1969c4f611c" class="bulleted-list"><li style="list-style-type:disc"><strong>11.4 Contacting Technical Support</strong></li></ul><h3 id="2262881f-86f1-8019-a3fc-f560c3746b59" class=""><strong>Security &amp; Compliance</strong></h3><p id="2262881f-86f1-8031-a27f-d8921ddea46f" class="">Explains security architecture, privacy enforcement, authentication models, and regulatory conformance practices. Role-based responsibility (e.g., security officer) is described in Section 8.1.</p><ul id="2262881f-86f1-80fa-bb3b-c7cd4298abbd" class="bulleted-list"><li style="list-style-type:disc"><strong>12.1 Data Encryption &amp; Privacy Controls</strong></li></ul><ul id="************************************" class="bulleted-list"><li style="list-style-type:disc"><strong>12.2 Authentication Methods &amp; Single Sign-On</strong></li></ul><ul id="2262881f-86f1-80b1-8727-d4f40445903e" class="bulleted-list"><li style="list-style-type:disc"><strong>12.3 Regulatory Compliance (GDPR, Local Codes)</strong></li></ul><ul id="2262881f-86f1-80e1-94ae-c5c9c497c234" class="bulleted-list"><li style="list-style-type:disc"><strong>12.4 Pen-testing &amp; Vulnerability Management</strong></li></ul><h3 id="2262881f-86f1-801b-806c-ecc3ce0463fd" class=""><strong>Appendices &amp; Index</strong></h3><p id="2262881f-86f1-8013-a3f7-d83846a362af" class="">Provides supplemental materials including formulas, standards, glossary terms, and documentation changelogs.</p><ul id="2262881f-86f1-80ab-993d-efc1dbb58b14" class="bulleted-list"><li style="list-style-type:disc"><strong>A. Calculation Methodologies &amp; Formulas</strong></li></ul><ul id="2262881f-86f1-8097-be97-f6c0e72b6148" class="bulleted-list"><li style="list-style-type:disc"><strong>B. Industry Standards &amp; Code References</strong></li></ul><ul id="2262881f-86f1-8029-ba5a-d501ec58f7df" class="bulleted-list"><li style="list-style-type:disc"><strong>C. Glossary of Terms &amp; Acronyms</strong></li></ul><ul id="2262881f-86f1-8016-8a29-decfc94380f9" class="bulleted-list"><li style="list-style-type:disc"><strong>D. Release Notes &amp; Version History</strong></li></ul><ul id="2262881f-86f1-8081-be18-ece1e8f343bb" class="bulleted-list"><li style="list-style-type:disc"><strong>E. Sample Project Files &amp; Templates</strong></li></ul><ul id="2262881f-86f1-8010-9ed2-e0685f6eeac4" class="bulleted-list"><li style="list-style-type:disc"><strong>Index</strong></li></ul><p id="2262881f-86f1-80cc-a1ba-d6c789859efc" class="">This structure supports the core philosophy of SizeWise: modular design, rigorous compliance, and role-based accessibility. As new features and modules are added to the Suite, the documentation will evolve with a consistent and maintainable architecture that ensures both internal coherence and external usability.</p></div></article><span class="sans" style="font-size:14px;padding-top:2em"></span></body></html>