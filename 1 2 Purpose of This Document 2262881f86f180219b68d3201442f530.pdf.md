# 1.2 Purpose of This Document

# Purpose of This Document

This document provides a comprehensive framework for the planning, development, validation, and support of the Boiler Vent Sizer—a specialized engineering tool within the SizeWise Suite. It serves as a central reference for all stakeholders, including developers responsible for functionality, QA teams ensuring reliability, standards consultants overseeing compliance, and project managers managing scope and timelines. Its primary goal is to ensure consistent alignment across disciplines in meeting all technical, regulatory, and usability standards.

The Boiler Vent Sizer is designed to deliver accurate, code-compliant vent sizing for gas- and oil-fired appliances across Categories I through IV. It supports both single and common venting systems while accounting for real-world variables such as draft pressure, pipe length, elevation, combustion temperature, and pressure loss. Its offline-first capability makes it particularly useful for field engineers working in mechanical rooms, rooftops, or remote basements where Wi-Fi or cellular connectivity is unavailable. Seamless integration with other SizeWise Suite modules ensures workflow consistency and data continuity.

This document is intended to be used throughout all stages of the tool’s lifecycle—from concept and prototyping through development, validation, deployment, and ongoing maintenance. It sets clear expectations for the tool’s features, user interactions, compliance logic, and quality benchmarks.

Key topics covered include the following grouped categories for clarity and alignment:

Tool Scope and System Boundaries: Defines what the Boiler Vent Sizer will do, what it will not do, and how it integrates within the broader SizeWise platform.

Code Compliance and Validation Rules: Outlines how industry standards such as NFPA 54 (ANSI Z223.1) and UL 1738 will be implemented, versioned, and documented, including through a centralized changelog and release notes to ensure traceability.

Appliance and System Types: Details the supported appliance categories and venting configurations, along with how the tool dynamically adjusts to each.

User Interface and Experience: Establishes expectations for UI design, form field behavior, input validation, and user feedback mechanisms.

Imperial and Metric Unit Handling: Specifies how the tool will handle dual-unit systems, including conversion logic, unit toggling, and output formatting.

Export Format Requirements: Describes the structure and content of generated outputs such as PDF, Excel,and CSV files for engineering, submittal, and bid documentation.

Testing Strategy and Success Metrics: Identifies testing requirements, including minimum 85% unit test coverage, end-to-end validations, and defined QA success criteria.

This document is considered a living specification and will be updated alongside future feature enhancements, standards revisions, or architectural changes. Any significant modification—such as a redesign, new feature, or standards update—must undergo internal review and be approved by the product lead and compliance officer, and must align with the specifications set forth herein. All development, testing, and maintenance decisions must trace back to this authoritative reference.

<!-- 1.2 Purpose of This Document -->

